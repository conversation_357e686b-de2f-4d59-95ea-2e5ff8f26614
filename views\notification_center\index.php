<?php
require_once APPROOT . '/views/includes/header.php';
require_once APPROOT . '/helpers/timezone_helper.php';
require_once APPROOT . '/core/ContentCleaner.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>Messages
                        <span class="badge bg-secondary ms-2" id="total-count"><?php echo $counts['total_count']; ?></span>
                    </h2>
                    <p class="text-muted mb-0">Manage your notifications and messages</p>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshPage()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Status Filter Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'all' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=all">
                        <span class="d-none d-md-inline">All Messages</span>
                        <span class="d-md-none"><i class="fas fa-list"></i></span>
                        <span class="badge bg-secondary ms-1" id="all-tab-count">...</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'unread' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=unread">
                        <span class="d-none d-md-inline">Unread</span>
                        <span class="d-md-none"><i class="fas fa-envelope"></i></span>
                        <span class="badge bg-danger ms-1" id="unread-tab-count">...</span>
                    </a>
                </li>
                <?php if (isset($has_email_access) && $has_email_access): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'email' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=email">
                        <span class="d-none d-md-inline">Email Messages</span>
                        <span class="d-md-none"><i class="fas fa-at"></i></span>
                        <span class="badge bg-info ms-1" id="email-tab-count"><?php echo $counts['email_count'] ?? 0; ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Email Management Tab (Admin only) -->
                <?php if (isset($user_role) && $user_role === 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'manage' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=manage">
                        <span class="d-none d-md-inline">Email Management</span>
                        <span class="d-md-none"><i class="fas fa-cogs"></i></span>
                        <span class="badge bg-warning ms-1" id="manage-tab-count"><?php echo $counts['manage_count'] ?? 0; ?></span>
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=archived">
                        <span class="d-none d-md-inline">Archived</span>
                        <span class="d-md-none"><i class="fas fa-archive"></i></span>
                        <span class="badge bg-secondary ms-1" id="archived-tab-count">...</span>
                    </a>
                </li>
            </ul>

            <!-- Bulk Actions -->
            <?php if ($current_status !== 'archived' && !empty($messages)): ?>
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all" onchange="toggleSelectAllMessages()">
                            <label class="form-check-label" for="select-all">
                                <small class="text-muted">Select all</small>
                            </label>
                        </div>
                        <div id="bulk-actions" style="display: none;">
                            <!-- Desktop bulk actions -->
                            <div class="d-none d-md-block">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-success" onclick="bulkMarkAsRead()">
                                        <i class="fas fa-check me-1"></i>Mark Read
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="bulkArchive()">
                                        <i class="fas fa-archive me-1"></i>Archive
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                        <i class="fas fa-trash me-1"></i>Delete
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearSelection()">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                </div>
                            </div>
                            <!-- Mobile bulk actions -->
                            <div class="d-md-none">
                                <div class="btn-group" role="group" style="display: flex; flex-wrap: nowrap;">
                                    <button type="button" class="btn btn-outline-success btn-sm" style="padding: 5px 9px;" onclick="bulkMarkAsRead()" title="Mark Read">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" style="padding: 5px 9px;" onclick="bulkArchive()" title="Archive">
                                        <i class="fas fa-archive"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" style="padding: 5px 9px;" onclick="bulkDelete()" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" style="padding: 5px 9px;" onclick="clearSelection()" title="Clear">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Email Management Section (Admin and Coordinator) -->
            <?php if ($current_status === 'manage' && isset($user_role) && in_array($user_role, ['admin', 'coordinator'])): ?>
                <div class="row">
                    <!-- Email Folders Sidebar -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-folder me-2"></i>Email Folders
                                    <button class="btn btn-sm btn-outline-primary float-end" onclick="showCreateFolderModal()">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="email-folders-list" class="list-group list-group-flush">
                                    <div class="text-center p-3">
                                        <i class="fas fa-spinner fa-spin"></i> Loading folders...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email Templates -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>Email Templates
                                    <button class="btn btn-sm btn-outline-primary float-end" onclick="showCreateTemplateModal()">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="email-templates-list" class="list-group list-group-flush">
                                    <div class="text-center p-3">
                                        <i class="fas fa-spinner fa-spin"></i> Loading templates...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email Reminders -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-bell me-2"></i>Active Reminders
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="email-reminders-list">
                                    <div class="text-center p-3">
                                        <i class="fas fa-spinner fa-spin"></i> Loading reminders...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Management Content -->
                    <div class="col-md-9">
                        <!-- Statistics Cards -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center py-2">
                                        <h4 id="total-emails">
                                            <?php echo isset($email_stats['total_emails']) ? $email_stats['total_emails'] : '0'; ?>
                                        </h4>
                                        <small>Total Emails</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center py-2">
                                        <h4 id="pending-emails">
                                            <?php echo isset($email_stats['follow_up']) ? $email_stats['follow_up'] : '0'; ?>
                                        </h4>
                                        <small>Follow Up</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center py-2">
                                        <h4 id="resolved-emails">
                                            <?php echo isset($email_stats['resolved']) ? $email_stats['resolved'] : '0'; ?>
                                        </h4>
                                        <small>Resolved</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Emails List -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-envelope me-2"></i>Recent Email Messages
                                    </h6>
                                    <div class="d-flex gap-2">
                                        <?php if ($user_role === 'admin'): ?>
                                            <!-- Admin: Search-based show filter for thousands of shows -->
                                            <div class="position-relative" style="min-width: 250px;">
                                                <input type="text"
                                                       class="form-control form-control-sm"
                                                       id="show-search"
                                                       placeholder="Search shows... (type to filter)"
                                                       autocomplete="off"
                                                       onkeyup="searchShows(this.value)"
                                                       onfocus="showSearchResults()"
                                                       style="padding-right: 30px;">
                                                <button class="btn btn-sm position-absolute top-0 end-0 h-100 border-0"
                                                        onclick="clearShowFilter()"
                                                        title="Clear filter"
                                                        style="background: none; z-index: 10;">
                                                    <i class="fas fa-times text-muted"></i>
                                                </button>
                                                <div id="show-search-results"
                                                     class="position-absolute w-100 bg-white border rounded shadow-sm d-none"
                                                     style="top: 100%; z-index: 1000; max-height: 300px; overflow-y: auto;">
                                                    <!-- Search results will appear here -->
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <!-- Coordinator: Smart filtered dropdown (active + completed < 1 week) -->
                                            <select class="form-select form-select-sm" id="coordinator-show-filter" onchange="filterByShow()" style="min-width: 200px;">
                                                <option value="">My Shows</option>
                                                <?php
                                                // Coordinator: Active shows + completed shows less than 1 week old
                                                $this->db->query("SELECT s.id, s.name, s.status, s.end_date
                                                                 FROM shows s
                                                                 INNER JOIN show_role_assignments sr ON s.id = sr.show_id
                                                                 WHERE sr.user_id = ? AND sr.role = 'coordinator'
                                                                 AND (s.status != 'completed' OR
                                                                      (s.status = 'completed' AND s.end_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)))
                                                                 ORDER BY s.status ASC, s.start_date DESC");
                                                $this->db->bind(1, $user_id);
                                                $shows = $this->db->resultSet();
                                                foreach ($shows as $show):
                                                    $statusBadge = $show->status === 'completed' ? ' (Completed)' : '';
                                                ?>
                                                    <option value="<?php echo $show->id; ?>">
                                                        <?php echo htmlspecialchars($show->name . $statusBadge); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        <?php endif; ?>
                                        <?php if ($user_role === 'admin'): ?>
                                            <button class="btn btn-success btn-sm" onclick="forceEmailCheck()" id="force-email-btn"
                                                    title="Manually trigger email processing cron job - useful for testing and immediate email retrieval">
                                                <i class="fas fa-download"></i> Force Email Check
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-outline-primary btn-sm" onclick="refreshEmailList()">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="showRecentShows()" title="Recent Shows">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <!-- Bulk Actions Bar -->
                                    <div id="bulk-actions-bar" class="d-none bg-light p-2 border-bottom">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span id="selected-count">0 selected</span>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="bulkMoveToFolder()">
                                                    <i class="fas fa-folder me-1"></i>Move to Folder
                                                </button>
                                                <button class="btn btn-outline-success" onclick="bulkMarkAsRead()">
                                                    <i class="fas fa-check me-1"></i>Mark as Read
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="bulkArchive()">
                                                    <i class="fas fa-archive me-1"></i>Archive
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="bulkDelete()">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="clearSelection()">
                                                    <i class="fas fa-times me-1"></i>Clear
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <table class="table table-sm mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="30">
                                                    <input type="checkbox" id="select-all-emails" onchange="toggleSelectAll(this)">
                                                </th>
                                                <th style="cursor: pointer;" onclick="sortEmailTable('subject')">
                                                    Subject <i class="fas fa-sort text-muted"></i>
                                                </th>
                                                <th style="cursor: pointer;" onclick="sortEmailTable('from')">
                                                    From <i class="fas fa-sort text-muted"></i>
                                                </th>
                                                <th style="cursor: pointer;" onclick="sortEmailTable('date')">
                                                    Date <i class="fas fa-sort text-muted"></i>
                                                </th>
                                                <th>Folder</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recent_emails)): ?>
                                                <tr>
                                                    <td colspan="6" class="text-center text-muted py-3">
                                                        No email messages found. Email messages will appear here when received.
                                                    </td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($recent_emails as $conversation): ?>
                                                <?php 
                                                    $rootMessage = $conversation['root_message'];
                                                    $totalMessages = $conversation['total_messages'];
                                                    $hasUnread = $conversation['has_unread'] ?? false;
                                                ?>
                                                <tr class="<?php echo $hasUnread ? 'table-warning' : ''; ?>">
                                                    <td>
                                                        <input type="checkbox" class="email-checkbox" value="<?php echo $rootMessage->id; ?>" onchange="updateSelection()">
                                                    </td>
                                                    <td>
                                                        <a href="<?= BASE_URL ?>/notification_center/viewMessage/<?php echo $rootMessage->id; ?>" class="text-decoration-none">
                                                            <div class="fw-bold text-primary" style="font-size: 13px;">
                                                                <?php echo htmlspecialchars(substr($rootMessage->subject, 0, 40)); ?>
                                                                <?php if ($totalMessages > 1): ?>
                                                                    <span class="badge bg-secondary ms-1" style="font-size: 10px;"><?php echo $totalMessages; ?></span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <small><?php echo htmlspecialchars($rootMessage->original_sender_email ?? 'System'); ?></small>
                                                    </td>
                                                    <td>
                                                        <small><?php echo formatDateTimeForUser($conversation['last_activity'], $_SESSION['user_id'], 'M j, g:i A'); ?></small>
                                                    </td>
                                                    <td>
                                                        <span class="badge" style="background-color: <?php echo $rootMessage->folder_color ?? '#007bff'; ?>; font-size: 10px;">
                                                            <?php echo $rootMessage->folder_name ?? 'Inbox'; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-secondary btn-sm p-1" onclick="showFolderSelectionModal(<?php echo $rootMessage->id; ?>)" title="Move to Folder" style="font-size: 10px;">
                                                                <i class="fas fa-folder"></i>
                                                            </button>
                                                            <button class="btn btn-outline-warning btn-sm p-1" onclick="showReminderModal(<?php echo $rootMessage->id; ?>)" title="Set Reminder" style="font-size: 10px;">
                                                                <i class="fas fa-bell"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Messages List (only show for non-management statuses) -->
            <?php if ($current_status !== 'manage'): ?>
                <?php if (empty($messages)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No messages found</h4>
                        <p class="text-muted">
                            <?php if ($current_status === 'unread'): ?>
                                You have no unread messages.
                            <?php elseif ($current_status === 'archived'): ?>
                                You have no archived messages.
                            <?php else: ?>
                                You have no messages yet.
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                <div class="message-list">
                    <?php 

                    

                    ?>
                    
                    <script>
                    // Update conversation counts in tabs
                    document.addEventListener('DOMContentLoaded', function() {
                        // Set all tab counts to conversation counts
                        document.getElementById('all-tab-count').textContent = '<?php echo $conversationCounts['all']; ?>';
                        document.getElementById('unread-tab-count').textContent = '<?php echo $conversationCounts['unread']; ?>';
                        document.getElementById('archived-tab-count').textContent = '<?php echo $conversationCounts['archived']; ?>';
                    });
                    </script>


                    
                    <?php foreach ($messages as $conversation): ?>
                        <?php 
                        // ===== CONVERSATION DATA SETUP =====
                        // Each $conversation contains a grouped thread of messages
                        $message = $conversation['root_message'];           // The original/first message in the thread
                        $replies = $conversation['replies'] ?? [];         // Array of reply messages (chronological order)
                        $totalMessages = $conversation['total_messages'] ?? 1; // Total count of messages in this thread
                        $hasUnread = $conversation['has_unread'] ?? !$message->is_read; // Does thread have unread messages?
                        $ticketNumber = $conversation['ticket_number'] ?? null; // Email ticket number (if applicable)
                        
                        // ===== DISPLAY MESSAGE LOGIC =====
                        // IMPORTANT: This determines what subject/content to show in the list view
                        // 
                        // EMAIL MESSAGES: Use root message ($message) - they have special logic elsewhere 
                        //                 that was already working correctly before this fix
                        // 
                        // OTHER TYPES: Use latest reply ($displayMessage) - this shows the most recent 
                        //              content instead of the original message content
                        //
                        $displayMessage = $message; // Default: use root message for display
                        
                        if (!empty($replies) && $message->message_type !== 'email') {
                            // For NON-email messages: show latest reply content in list view
                            // This makes direct/system/other messages behave consistently
                            $displayMessage = end($replies); // end() gets the last element (latest reply)
                        }
                        // Note: $message is still used for navigation, metadata, and email-specific logic
                        
                        // ===== VISUAL STATE SETUP =====
                        $showAsUnread = $hasUnread; // Controls unread badges and styling
                        
                        // ===== CONVERSATION CLASSES =====
                        // CSS classes for different conversation types
                        $conversationClasses = '';
                        if ($totalMessages > 1) {
                            $conversationClasses .= ' conversation-thread'; // Multi-message thread styling
                        }
                        if ($ticketNumber) {
                            $conversationClasses .= ' ticket-thread'; // Email ticket styling
                        }
                        ?>
                        <div class="card mb-2 message-item <?php echo $showAsUnread ? 'message-unread' : ''; ?><?php echo $conversationClasses; ?>"
                             data-message-id="<?php echo $message->id; ?>">
                            <div class="card-body py-2">
                                <!-- Mobile Layout -->
                                <div class="d-md-none">
                                    <div class="d-flex align-items-start">
                                        <!-- Mobile Checkbox (smaller) -->
                                        <?php if ($current_status !== 'archived'): ?>
                                        <div class="flex-shrink-0 me-2">
                                            <?php 
                                            // Create array of all message IDs in this conversation
                                            $conversationMessageIds = [$message->id];
                                            foreach ($replies as $reply) {
                                                $conversationMessageIds[] = $reply->id;
                                            }
                                            $conversationIdsJson = json_encode($conversationMessageIds);
                                            ?>
                                            <input class="form-check-input message-checkbox" type="checkbox"
                                                   value="<?php echo $message->id; ?>" 
                                                   data-conversation-ids="<?php echo htmlspecialchars($conversationIdsJson); ?>"
                                                   onchange="updateBulkActions()"
                                                   style="margin-top: 2px;">
                                        </div>
                                        <?php endif; ?>

                                        <!-- Mobile Icon (smaller) -->
                                        <div class="flex-shrink-0 me-2">
                                            <?php
                                            $iconClass = 'fas fa-envelope';
                                            $iconColor = 'text-primary';

                                            switch($message->message_type) {
                                                case 'show_notification':
                                                    $iconClass = 'fas fa-car';
                                                    $iconColor = 'text-success';
                                                    break;
                                                case 'system':
                                                    $iconClass = 'fas fa-cog';
                                                    $iconColor = 'text-info';
                                                    break;
                                                case 'reply':
                                                    $iconClass = 'fas fa-reply';
                                                    $iconColor = 'text-warning';
                                                    break;
                                                default:
                                                    $iconClass = 'fas fa-envelope';
                                                    $iconColor = 'text-primary';
                                            }
                                            ?>
                                            <i class="<?php echo $iconClass . ' ' . $iconColor; ?>" style="font-size: 14px;"></i>
                                        </div>

                                        <!-- Mobile Content -->
                                        <div class="flex-grow-1 min-width-0 mobile-message-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1 min-width-0">
                                                    <!-- MOBILE: Message Subject -->
                                                    <!-- Uses $displayMessage->subject (latest reply for non-emails, root for emails) -->
                                                    <!-- Links to $message->id (always root message for proper threading) -->
                                                    <h6 class="mb-1 <?php echo !$message->is_read ? 'fw-bold' : ''; ?>" style="font-size: 14px;">
                                                        <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>"
                                                           class="text-decoration-none text-dark">
                                                            <?php echo htmlspecialchars($displayMessage->subject); ?>
                                                        </a>

                                                        <?php if ($showAsUnread): ?>
                                                            <span class="badge bg-danger ms-1" style="font-size: 10px;">New</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <!-- MOBILE: Message Preview -->
                                                    <!-- Uses $displayMessage->message (latest reply content for non-emails, root for emails) -->
                                                    <p class="mb-1 text-muted" style="font-size: 12px;">
                                                        <?php 
                                                        $cleanPreview = ContentCleaner::cleanMessageContent($displayMessage->message);
                                                        echo htmlspecialchars(substr($cleanPreview, 0, 60)); 
                                                        ?>
                                                        <?php if (strlen($cleanPreview) > 60): ?>...<?php endif; ?>
                                                    </p>
                                                    <small class="text-muted" style="font-size: 11px;">
                                                        <?php
                                                        // Display sender name with small role badge
                                                        $senderName = htmlspecialchars($message->from_user_name ?? 'System');
                                                        $messageType = $message->message_type ?? 'direct';

                                                        echo $senderName;

                                                        // Add small role badge after name
                                                        switch($messageType) {
                                                            case 'admin':
                                                                echo ' <span class="badge bg-danger ms-1" style="font-size: 8px;">Admin</span>';
                                                                break;
                                                            case 'coordinator':
                                                                echo ' <span class="badge bg-primary ms-1" style="font-size: 8px;">Coordinator</span>';
                                                                break;
                                                            case 'judge':
                                                                echo ' <span class="badge bg-success ms-1" style="font-size: 8px;">Judge</span>';
                                                                break;
                                                            case 'staff':
                                                                echo ' <span class="badge bg-warning text-dark ms-1" style="font-size: 8px;">Staff</span>';
                                                                break;
                                                            case 'system':
                                                                echo ' <span class="badge bg-info ms-1" style="font-size: 8px;">System</span>';
                                                                break;
                                                        }
                                                        ?>
                                                        • <?php echo formatDateTimeForUser($message->created_at, $_SESSION['user_id'], 'M j, g:i A'); ?>
                                                    </small>
                                                </div>
                                                <div class="flex-shrink-0 ms-2">
                                                    <div class="btn-group btn-group-sm" style="display: flex; flex-wrap: wrap;">
                                                        <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>"
                                                           class="btn btn-outline-primary btn-sm" style="padding: 3px 7px; font-size: 11px; margin-bottom: 2px;" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if (!$message->is_read): ?>
                                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                                    style="padding: 3px 7px; font-size: 11px; margin-bottom: 2px;"
                                                                    onclick="markAsRead(<?php echo $message->id; ?>)" title="Mark Read">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <?php endif; ?>

                                                        <!-- Reply Button (mobile) -->
                                                        <?php if ($message->message_type === 'email' || $message->requires_reply || $message->message_type === 'direct'): ?>
                                                            <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>#reply"
                                                               class="btn btn-outline-info btn-sm" style="padding: 3px 7px; font-size: 11px; margin-bottom: 2px;" title="Reply">
                                                                <i class="fas fa-reply"></i>
                                                            </a>
                                                        <?php endif; ?>

                                                        <?php if ($current_status === 'archived'): ?>
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    style="padding: 3px 7px; font-size: 11px; margin-bottom: 2px;"
                                                                    onclick="unarchiveMessage(<?php echo $message->id; ?>)" title="Restore">
                                                                <i class="fas fa-undo"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                                    style="padding: 3px 7px; font-size: 11px; margin-bottom: 2px;"
                                                                    onclick="archiveMessage(<?php echo $message->id; ?>)" title="Archive">
                                                                <i class="fas fa-archive"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Desktop Layout -->
                                <div class="d-none d-md-block">
                                    <div class="row align-items-center">
                                        <!-- Checkbox -->
                                        <?php if ($current_status !== 'archived'): ?>
                                        <div class="col-auto" style="width: 30px;">
                                            <div class="form-check">
                                                <input class="form-check-input message-checkbox" type="checkbox"
                                                       value="<?php echo $message->id; ?>"
                                                       data-conversation-ids="<?php echo htmlspecialchars($conversationIdsJson); ?>"
                                                       onchange="updateBulkActions()">
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Message Icon -->
                                        <div class="col-auto">
                                            <div class="message-icon">
                                                <i class="<?php echo $iconClass . ' ' . $iconColor; ?>"></i>
                                            </div>
                                        </div>

                                        <!-- Message Content -->
                                        <div class="col">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <!-- DESKTOP: Message Subject -->
                                                <!-- Uses $displayMessage->subject (latest reply for non-emails, root for emails) -->
                                                <!-- Links to $message->id (always root message for proper threading) -->
                                                <h6 class="mb-1 <?php echo !$message->is_read ? 'fw-bold' : ''; ?>">
                                                    <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>" 
                                                       class="text-decoration-none text-dark">
                                                        <?php echo htmlspecialchars($displayMessage->subject); ?>
                                                    </a>
                                                    
                                                    <?php if ($showAsUnread): ?>
                                                        <span class="badge bg-danger ms-2">Unread</span>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Conversation Indicators -->
                                                    <?php if ($totalMessages > 1): ?>
                                                        <span class="badge bg-info ms-2" title="Conversation thread">
                                                            <i class="fas fa-comments me-1"></i><?php echo $totalMessages; ?> message<?php echo $totalMessages == 1 ? '' : 's'; ?>
                                                        </span>
                                                        
                                                        <?php if ($hasUnread): ?>
                                                            <span class="badge bg-danger ms-1" title="Has unread messages">
                                                                <i class="fas fa-exclamation me-1"></i>Unread
                                                            </span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($ticketNumber): ?>
                                                        <span class="badge bg-secondary ms-2" title="Ticket Number">
                                                            <i class="fas fa-ticket-alt me-1"></i><?php echo htmlspecialchars($ticketNumber); ?>
                                                        </span>
                                                    <?php endif; ?>
                                                    

                                                </h6>
                                                <!-- DESKTOP: Message Preview -->
                                                <!-- Uses $displayMessage->message (latest reply content for non-emails, root for emails) -->
                                                <p class="mb-1 text-muted small">
                                                    <?php if (!empty($selfReplies)): ?>
                                                        <i class="fas fa-layer-group me-1 text-warning"></i><strong>Conversation:</strong> 
                                                    <?php endif; ?>
                                                    <?php 
                                                    $cleanPreview = ContentCleaner::cleanMessageContent($displayMessage->message);
                                                    echo htmlspecialchars(substr($cleanPreview, 0, 100)); 
                                                    ?>
                                                    <?php if (strlen($cleanPreview) > 100): ?>...<?php endif; ?>
                                                    <?php if (!empty($selfReplies)): ?>
                                                        <br><small class="text-warning"><i class="fas fa-info-circle me-1"></i>This includes <?php echo count($selfReplies); ?> of your replies (consolidated view).</small>
                                                    <?php endif; ?>
                                                </p>
                                                <!-- DESKTOP: Show Information -->
                                                <?php if (!empty($message->show_title)): ?>
                                                <p class="mb-1 text-muted small">
                                                    <i class="fas fa-car me-1"></i>Show:
                                                    <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $message->show_id; ?>"
                                                       class="text-decoration-none text-primary"
                                                       title="View show details">
                                                        <?php echo htmlspecialchars($message->show_title); ?>
                                                    </a>
                                                    <?php if (!empty($message->show_location)): ?>
                                                        <span class="text-muted">• <?php echo htmlspecialchars($message->show_location); ?></span>
                                                    <?php endif; ?>
                                                </p>
                                                <?php endif; ?>
                                                <!-- DESKTOP: Sender Information -->
                                                <!-- Uses $message (root message) for sender info - this should always be the original sender -->
                                                <small class="text-muted">
                                                    From:
                                                    <?php
                                                    // Display sender name with small role badge
                                                    // Uses $message (root) because we want original sender, not latest replier
                                                    $messageType = $message->message_type ?? 'direct';

                                                    // For email messages, show original sender email if available
                                                    if ($messageType === 'email' && !empty($message->original_sender_email)) {
                                                        $senderName = htmlspecialchars($message->original_sender_email);
                                                        if (!empty($message->from_user_name)) {
                                                            $senderName .= ' (' . htmlspecialchars($message->from_user_name) . ')';
                                                        }
                                                    } else {
                                                        $senderName = htmlspecialchars($message->from_user_name ?? 'System');
                                                    }

                                                    echo $senderName;

                                                    // Add small role badge after name
                                                    switch($messageType) {
                                                        case 'email':
                                                            echo ' <span class="badge bg-secondary ms-1" style="font-size: 9px;">Email</span>';
                                                            // Ticket badge removed to give more room for show information
                                                            break;
                                                        case 'admin':
                                                            echo ' <span class="badge bg-danger ms-1" style="font-size: 9px;">Admin</span>';
                                                            break;
                                                        case 'coordinator':
                                                            echo ' <span class="badge bg-primary ms-1" style="font-size: 9px;">Coordinator</span>';
                                                            break;
                                                        case 'judge':
                                                            echo ' <span class="badge bg-success ms-1" style="font-size: 9px;">Judge</span>';
                                                            break;
                                                        case 'staff':
                                                            echo ' <span class="badge bg-warning text-dark ms-1" style="font-size: 9px;">Staff</span>';
                                                            break;
                                                        case 'system':
                                                            echo ' <span class="badge bg-info ms-1" style="font-size: 9px;">System</span>';
                                                            break;
                                                    }
                                                    ?>

                                                    <?php if ($messageType === 'email' && isset($has_email_access) && $has_email_access): ?>
                                                        <?php if (!empty($message->owned_by_admin_id)): ?>
                                                            • Owned by: <?php echo htmlspecialchars($message->owner_name ?? 'Admin'); ?>
                                                        <?php else: ?>
                                                            • <span class="text-warning">Unassigned</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <!-- DESKTOP: Timestamp Information -->
                                            <div class="col-md-4 text-end">
                                                <small class="text-muted d-block">
                                                    <?php if ($totalMessages > 1 && isset($conversation['last_activity'])): ?>
                                                        <!-- Multi-message thread: show both latest activity and original start time -->
                                                        <i class="fas fa-clock me-1"></i>Latest: <?php echo formatDateTimeForUser($conversation['last_activity'], $_SESSION['user_id'], 'M j, Y g:i A'); ?>
                                                        <br><span class="text-muted">Started: <?php echo formatDateTimeForUser($message->created_at, $_SESSION['user_id'], 'M j, Y g:i A'); ?></span>
                                                    <?php else: ?>
                                                        <!-- Single message: show creation time -->
                                                        <i class="fas fa-clock me-1"></i><?php echo formatDateTimeForUser($message->created_at, $_SESSION['user_id'], 'M j, Y g:i A'); ?>
                                                    <?php endif; ?>
                                                </small>
                                                
                                                <!-- DESKTOP: Action Buttons -->
                                                <!-- All actions use $message->id (root message) for proper threading and navigation -->
                                                <div class="btn-group btn-group-sm mt-2" role="group">
                                                    <!-- View Button: Always links to root message for proper thread display -->
                                                    <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>" 
                                                       class="btn btn-outline-primary btn-sm" title="View Message">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    
                                                    <!-- Mark as Read (only for unread messages) -->
                                                    <?php if (!$message->is_read): ?>
                                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                                onclick="markAsRead(<?php echo $message->id; ?>)" title="Mark as Read">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Reply (always available for emails, or messages that allow replies) -->
                                                    <?php if ($message->message_type === 'email' || $message->requires_reply || $message->message_type === 'direct'): ?>
                                                        <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>#reply"
                                                           class="btn btn-outline-info btn-sm" title="Reply">
                                                            <i class="fas fa-reply"></i>
                                                        </a>
                                                    <?php endif; ?>

                                                    <!-- Email-specific quick actions for admin/coordinator -->
                                                    <?php if ($message->message_type === 'email' && isset($has_email_access) && $has_email_access && $current_status === 'email'): ?>
                                                        <?php if ($user_role === 'admin'): ?>
                                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setEmailReminder(<?php echo $message->id; ?>)" title="Set Reminder">
                                                            <i class="fas fa-bell"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="moveToFolder(<?php echo $message->id; ?>)" title="Move to Folder">
                                                            <i class="fas fa-folder"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="transferOwnership(<?php echo $message->id; ?>)" title="Transfer Ownership">
                                                            <i class="fas fa-user-friends"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="quickReply(<?php echo $message->id; ?>)" title="Quick Reply">
                                                            <i class="fas fa-reply"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Archive/Delete based on status -->
                                                    <?php if ($current_status === 'archived'): ?>
                                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                                onclick="unarchiveMessage(<?php echo $message->id; ?>)" title="Restore">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                                onclick="deleteMessage(<?php echo $message->id; ?>)" title="Delete Permanently">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                                onclick="archiveMessage(<?php echo $message->id; ?>)" title="Archive">
                                                            <i class="fas fa-archive"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ((int)$total_pages > 1): ?>
                    <nav aria-label="Messages pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ((int)$current_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?status=<?php echo $current_status; ?>&page=<?php echo (int)$current_page - 1; ?>">
                                        Previous
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, (int)$current_page - 2); $i <= min((int)$total_pages, (int)$current_page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == (int)$current_page ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?status=<?php echo $current_status; ?>&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ((int)$current_page < (int)$total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?status=<?php echo $current_status; ?>&page=<?php echo (int)$current_page + 1; ?>">
                                        Next
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
            <?php endif; ?> <!-- Close the message list conditional -->
        </div>
    </div>
</div>

<!-- Email Management Modals -->
<!-- Create/Edit Folder Modal -->
<div class="modal fade" id="folderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="folderModalTitle">Create Folder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="folderForm">
                    <input type="hidden" id="folderId" name="folder_id">
                    <div class="mb-3">
                        <label for="folderName" class="form-label">Folder Name</label>
                        <input type="text" class="form-control" id="folderName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="folderDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="folderDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="folderColor" class="form-label">Color</label>
                            <input type="color" class="form-control form-control-color" id="folderColor" name="color" value="#007bff">
                        </div>
                        <div class="col-md-6">
                            <label for="folderIcon" class="form-label">Icon</label>
                            <select class="form-control" id="folderIcon" name="icon">
                                <option value="fas fa-folder">📁 Folder</option>
                                <option value="fas fa-inbox">📥 Inbox</option>
                                <option value="fas fa-star">⭐ Star</option>
                                <option value="fas fa-clock">🕐 Clock</option>
                                <option value="fas fa-check-circle">✅ Check</option>
                                <option value="fas fa-ban">🚫 Ban</option>
                                <option value="fas fa-archive">📦 Archive</option>
                                <option value="fas fa-flag">🚩 Flag</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveFolderModal()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Template Modal -->
<div class="modal fade" id="templateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templateModalTitle">Create Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <input type="hidden" id="templateId" name="template_id">
                    <div class="row">
                        <div class="col-md-8">
                            <label for="templateName" class="form-label">Template Name</label>
                            <input type="text" class="form-control" id="templateName" name="name" required>
                        </div>
                        <div class="col-md-4">
                            <label for="templateCategory" class="form-label">Category</label>
                            <select class="form-control" id="templateCategory" name="category">
                                <option value="general">General</option>
                                <option value="events">Events</option>
                                <option value="registration">Registration</option>
                                <option value="followup">Follow Up</option>
                                <option value="support">Support</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3 mt-3">
                        <label for="templateSubject" class="form-label">Email Subject</label>
                        <input type="text" class="form-control" id="templateSubject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="templateBody" class="form-label">Email Body</label>
                        <textarea class="form-control" id="templateBody" name="body" rows="8" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Available Variables</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertVariable('{{subject}}')">{{subject}}</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertVariable('{{admin_name}}')">{{admin_name}}</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertVariable('{{date}}')">{{date}}</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertVariable('{{ticket_number}}')">{{ticket_number}}</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertVariable('{{sender_name}}')">{{sender_name}}</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertVariable('{{sender_email}}')">{{sender_email}}</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveTemplateModal()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Set Reminder Modal -->
<div class="modal fade" id="reminderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reminderModalTitle">Set Reminder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="reminderForm">
                    <input type="hidden" id="reminderMessageId" name="message_id">
                    <input type="hidden" id="reminderId" name="reminder_id">
                    <div class="mb-3">
                        <label for="reminderDate" class="form-label">Reminder Date & Time</label>
                        <input type="datetime-local" class="form-control" id="reminderDate" name="reminder_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="reminderText" class="form-label">Reminder Note (Optional)</label>
                        <textarea class="form-control" id="reminderText" name="reminder_text" rows="3" placeholder="Add a note about what needs to be done..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quick Options</label>
                        <div class="btn-group-vertical d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickReminder(1)">1 Hour</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickReminder(24)">Tomorrow</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickReminder(168)">Next Week</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveReminderModal()">Set Reminder</button>
            </div>
        </div>
    </div>
</div>

<!-- Move to Folder Modal -->
<div class="modal fade" id="moveToFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Move to Folder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="moveMessageId">
                <div class="mb-3">
                    <label for="targetFolder" class="form-label">Select Folder</label>
                    <div id="folderList" class="list-group">
                        <!-- Folders will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Email Statistics Modal -->
<div class="modal fade" id="statisticsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="statisticsContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Loading statistics...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.message-unread {
    border-left: 5px solid #007bff !important;
    background-color: #e3f2fd !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2) !important;
}

.message-unread .card-body {
    background-color: #e3f2fd !important;
}

.message-unread h6,
.message-unread h6 a {
    font-weight: bold !important;
    color: #0056b3 !important;
}

.message-unread .message-icon {
    position: relative;
}

.message-unread .message-icon::after {
    content: "●";
    color: #dc3545;
    font-size: 8px;
    position: absolute;
    top: -2px;
    right: -2px;
    background: white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.message-item {
    transition: all 0.3s ease;
    position: relative;
}

/* Conversation Threading Styles */
.message-item .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.message-item .badge.bg-info {
    background-color: #17a2b8 !important;
}

.message-item .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.message-item .badge.bg-danger {
    background-color: #dc3545 !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Conversation indicators */
.conversation-thread {
    border-left: 3px solid #17a2b8;
    background: linear-gradient(90deg, rgba(23,162,184,0.1) 0%, rgba(255,255,255,0) 20%);
}

.self-reply-indicator {
    border-left: 3px solid #ffc107;
    background: linear-gradient(90deg, rgba(255,193,7,0.1) 0%, rgba(255,255,255,0) 20%);
}
}

.message-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.message-icon {
    width: 40px;
    text-align: center;
}

.message-item .card-body {
    padding: 0.75rem 1rem;
}

.message-item .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
}

.message-item .btn-group-sm .btn i {
    font-size: 0.7rem;
}

@media (max-width: 768px) {
    .message-item .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .message-item .btn {
        margin-bottom: 0.25rem;
    }

    #bulk-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }

    #bulk-actions .btn {
        margin-bottom: 0.25rem;
    }

    /* Mobile-specific optimizations */
    .card-body {
        padding: 0.75rem !important;
    }

    .message-item .card-body {
        padding: 0.5rem !important;
    }

    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 14px;
    }

    .btn-group-vertical .btn {
        margin-bottom: 2px;
    }

    .btn-group-vertical .btn:last-child {
        margin-bottom: 0;
    }

    /* Ensure no horizontal scrolling */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Compact mobile message layout */
    .mobile-message-content {
        line-height: 1.3;
    }

    .mobile-message-content h6 {
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }

    .mobile-message-content p {
        line-height: 1.3;
        margin-bottom: 0.25rem;
    }

    /* Mobile tab icons */
    .nav-tabs .nav-link i {
        font-size: 16px;
    }

    /* Mobile bulk actions - force horizontal layout */
    .d-md-none .btn-group {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
    }

    .d-md-none .btn-group .btn {
        padding: 5px 9px !important;
        font-size: 13px !important;
        margin-right: 2px !important;
        flex-shrink: 0 !important;
    }

    .d-md-none .btn-group .btn:last-child {
        margin-right: 0 !important;
    }

    /* Mobile message action buttons */
    .d-md-none .message-item .btn-group {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: wrap !important;
    }

    .d-md-none .message-item .btn-group .btn {
        padding: 3px 7px !important;
        font-size: 11px !important;
        margin-right: 2px !important;
        margin-bottom: 2px !important;
        flex-shrink: 0 !important;
    }

    .d-md-none .message-item .btn-group .btn:last-child {
        margin-right: 0 !important;
    }

    /* Mobile message cards - more compact */
    .d-md-none .message-item {
        margin-bottom: 0.5rem !important;
    }

    .d-md-none .message-item .card-body {
        padding: 0.5rem !important;
    }
}

/* Conversation thread styling */
.conversation-thread {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(90deg, rgba(23,162,184,0.1) 0%, rgba(255,255,255,0) 20%);
}

.ticket-thread {
    border-left: 4px solid #6c757d;
    background: linear-gradient(90deg, rgba(108,117,125,0.1) 0%, rgba(255,255,255,0) 20%);
}

.conversation-thread.ticket-thread {
    border-left: 4px solid #28a745;
    background: linear-gradient(90deg, rgba(40,167,69,0.1) 0%, rgba(255,255,255,0) 20%);
}

/* Conversation indicators */
.badge.bg-info {
    background-color: #17a2b8 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}
</style>

<script>
// Global variables
let selectedMessages = new Set();

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    

});

function initializeEventListeners() {
    // Note: Select all and individual checkboxes now use onchange attributes directly
    // No need for additional event listeners
}

function toggleSelectAllMessages() {
    const selectAll = document.getElementById('select-all');
    if (!selectAll) return;
    
    const checkboxes = document.querySelectorAll('.message-checkbox');
    
    // Simply check/uncheck all boxes - let updateBulkActions handle the selection tracking
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectAll = document.getElementById('select-all');
    
    // Update selected messages set - include all conversation messages
    selectedMessages.clear();
    checkboxes.forEach(cb => {
        // Add the main message ID
        selectedMessages.add(cb.value);
        
        // Add all conversation message IDs if they exist
        const conversationIds = cb.getAttribute('data-conversation-ids');
        if (conversationIds) {
            try {
                const ids = JSON.parse(conversationIds);
                ids.forEach(id => selectedMessages.add(id.toString()));
            } catch (e) {
                console.warn('Failed to parse conversation IDs:', conversationIds);
            }
        }
    });
    
    // Show/hide bulk actions
    if (bulkActions) {
        bulkActions.style.display = checkboxes.length > 0 ? 'block' : 'none';
    }
    
    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.message-checkbox');
    if (selectAll && allCheckboxes.length > 0) {
        selectAll.checked = checkboxes.length === allCheckboxes.length;
        selectAll.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.message-checkbox');
    const selectAll = document.getElementById('select-all');
    
    checkboxes.forEach(checkbox => checkbox.checked = false);
    if (selectAll) selectAll.checked = false;
    
    selectedMessages.clear();
    updateBulkActions();
}

// Individual message actions
function markAsRead(messageId) {
    fetch('<?php echo BASE_URL; ?>/notification_center/markRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to mark as read: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to mark message as read');
    });
}

function archiveMessage(messageId) {
    // First get thread info
    fetch('<?php echo BASE_URL; ?>/notification_center/getThreadInfo', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(threadInfo => {
        console.log('Archive - Thread info response:', threadInfo); // Debug
        if (threadInfo.success) {
            let confirmMessage;
            if (threadInfo.total_messages > 1) {
                confirmMessage = `Archive this entire conversation thread (${threadInfo.total_messages} messages)?`;
            } else {
                confirmMessage = 'Archive this message?';
            }
            
            if (!confirm(confirmMessage)) return;
            
            // Proceed with archiving
            fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'message_id=' + messageId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to archive: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to archive thread');
            });
        } else {
            console.log('Archive - Thread info failed:', threadInfo);
            alert('Could not get thread information: ' + (threadInfo.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error getting thread info:', error);
        alert('Failed to get thread information');
    });
}

function deleteMessage(messageId) {
    // First get thread info
    fetch('<?php echo BASE_URL; ?>/notification_center/getThreadInfo', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(threadInfo => {
        console.log('Delete - Thread info response:', threadInfo); // Debug
        if (threadInfo.success) {
            let confirmMessage;
            if (threadInfo.total_messages > 1) {
                confirmMessage = `Permanently delete this entire conversation thread (${threadInfo.total_messages} messages)? This cannot be undone.`;
            } else {
                confirmMessage = 'Permanently delete this message? This cannot be undone.';
            }
            
            if (!confirm(confirmMessage)) return;
            
            // Proceed with deletion
            fetch('<?php echo BASE_URL; ?>/notification_center/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'message_id=' + messageId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to delete: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to delete thread');
            });
        } else {
            console.log('Delete - Thread info failed:', threadInfo);
            alert('Could not get thread information: ' + (threadInfo.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error getting thread info:', error);
        alert('Failed to get thread information');
    });
}

function unarchiveMessage(messageId) {
    if (!confirm('Restore this message to your active list?')) return;
    
    fetch('<?php echo BASE_URL; ?>/notification_center/unarchive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to restore: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to restore message');
    });
}

// Bulk actions
function bulkMarkAsRead() {
    if (selectedMessages.size === 0) {
        alert('Please select messages to mark as read');
        return;
    }
    
    if (!confirm(`Mark ${selectedMessages.size} message(s) as read?`)) return;
    
    const promises = Array.from(selectedMessages).map(messageId => {
        return fetch('<?php echo BASE_URL; ?>/notification_center/markRead', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'message_id=' + messageId
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failed = results.filter(r => !r.success);
            if (failed.length === 0) {
                location.reload();
            } else {
                alert(`${failed.length} message(s) could not be marked as read`);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Bulk mark as read failed:', error);
            alert('Some messages could not be marked as read');
        });
}

function bulkArchive() {
    if (selectedMessages.size === 0) {
        alert('Please select messages to archive');
        return;
    }
    
    if (!confirm(`Archive ${selectedMessages.size} message(s)?`)) return;
    
    const promises = Array.from(selectedMessages).map(messageId => {
        return fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'message_id=' + messageId
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failed = results.filter(r => !r.success);
            if (failed.length === 0) {
                location.reload();
            } else {
                alert(`${failed.length} message(s) could not be archived`);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Bulk archive failed:', error);
            alert('Some messages could not be archived');
        });
}

function bulkDelete() {
    if (selectedMessages.size === 0) {
        alert('Please select messages to delete');
        return;
    }
    
    if (!confirm(`Permanently delete ${selectedMessages.size} message(s)? This cannot be undone.`)) return;
    
    const promises = Array.from(selectedMessages).map(messageId => {
        return fetch('<?php echo BASE_URL; ?>/notification_center/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'message_id=' + messageId
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failed = results.filter(r => !r.success);
            if (failed.length === 0) {
                location.reload();
            } else {
                alert(`${failed.length} message(s) could not be deleted`);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Bulk delete failed:', error);
            alert('Some messages could not be deleted');
        });
}

function refreshPage() {
    location.reload();
}

// Email-specific quick action functions for admin/coordinator
function setEmailReminder(messageId) {
    // Show reminder modal
    showReminderModal(messageId);
}

function moveToFolder(messageId) {
    // Show folder selection modal
    showFolderSelectionModal(messageId);
}

function transferOwnership(messageId) {
    // Show ownership transfer modal
    showOwnershipTransferModal(messageId);
}

function quickReply(messageId) {
    // Redirect to message view with reply focus
    window.location.href = '<?= BASE_URL ?>/notification_center/viewMessage/' + messageId + '#reply';
}

// Email Management Functions
function loadEmailFolders() {
    console.log('Loading email folders...');
    fetch('<?= BASE_URL ?>/notification_center/getFolders')
        .then(response => {
            console.log('Folders response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Folders data:', data);
            if (data.success) {
                displayEmailFolders(data.folders);
            } else {
                console.error('Error loading folders:', data.message);
                document.getElementById('email-folders-list').innerHTML = '<div class="text-center p-3 text-danger">Error: ' + (data.message || 'Unknown error') + '</div>';
            }
        })
        .catch(error => {
            console.error('Fetch error loading folders:', error);
            document.getElementById('email-folders-list').innerHTML = '<div class="text-center p-3 text-danger">Network error loading folders: ' + error.message + '</div>';
        });
}

function displayEmailFolders(folders) {
    let html = '';
    folders.forEach(folder => {
        // Debug: Log folder data to see what we're getting
        console.log('Folder data:', folder);
        console.log('is_system value:', folder.is_system, 'type:', typeof folder.is_system);

        // Handle both string and numeric values for is_system
        // Custom folders should have is_system = 0, false, "0", or null
        // System folders have is_system = 1, true, or "1"
        const isSystemFolder = folder.is_system === 1 || folder.is_system === "1" || folder.is_system === true;
        const canEdit = !isSystemFolder;
        console.log('isSystemFolder:', isSystemFolder, 'canEdit for folder', folder.name, ':', canEdit);

        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center py-2"
                 style="cursor: pointer;"
                 onclick="filterByFolder(${folder.id}, '${folder.name}')"
                 title="Click to view emails in this folder">
                <div class="d-flex align-items-center flex-grow-1">
                    <i class="${folder.icon}" style="color: ${folder.color}; font-size: 14px;"></i>
                    <span class="ms-2 small">${folder.name}</span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-secondary me-2">${folder.message_count || 0}</span>
                    ${canEdit ? `
                        <div class="btn-group btn-group-sm" onclick="event.stopPropagation();">
                            <button class="btn btn-outline-secondary btn-sm p-1" onclick="showEditFolderModal(${folder.id}, '${folder.name}', '${folder.description}', '${folder.color}', '${folder.icon}')" title="Edit" style="font-size: 10px;">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm p-1" onclick="deleteFolder(${folder.id})" title="Delete" style="font-size: 10px;">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    });
    document.getElementById('email-folders-list').innerHTML = html;
}

function filterByFolder(folderId, folderName) {
    // Update the email list to show only emails from this folder
    fetch(`<?= BASE_URL ?>/notification_center/getEmailsByFolder?folder_id=${folderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateEmailTable(data.emails);
                showToast('success', `Showing emails in ${folderName} (${data.emails.length} emails)`);

                // Update the header to show current filter
                const header = document.querySelector('.card-header h6');
                if (header) {
                    header.innerHTML = `<i class="fas fa-envelope me-2"></i>Emails in ${folderName}`;
                }
            } else {
                showToast('error', 'Error loading folder emails: ' + data.message);
            }
        })
        .catch(error => {
            showToast('error', 'Network error loading folder emails');
        });
}

function showCreateFolderModal() {
    document.getElementById('folderModalTitle').textContent = 'Create Folder';
    document.getElementById('folderForm').reset();
    document.getElementById('folderId').value = '';
    new bootstrap.Modal(document.getElementById('folderModal')).show();
}

function showEditFolderModal(folderId, name, description, color, icon) {
    document.getElementById('folderModalTitle').textContent = 'Edit Folder';
    document.getElementById('folderId').value = folderId;
    document.getElementById('folderName').value = name;
    document.getElementById('folderDescription').value = description;
    document.getElementById('folderColor').value = color;
    document.getElementById('folderIcon').value = icon;
    new bootstrap.Modal(document.getElementById('folderModal')).show();
}

function saveFolderModal() {
    const formData = new FormData(document.getElementById('folderForm'));
    const folderId = document.getElementById('folderId').value;
    const url = folderId ? '<?= BASE_URL ?>/notification_center/updateFolder' : '<?= BASE_URL ?>/notification_center/createFolder';

    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('folderModal')).hide();
            loadEmailFolders();
            showToast('success', data.message);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        showToast('error', 'Error saving folder');
    });
}

function createFolder(name) {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('description', '');
    formData.append('color', '#007bff');
    formData.append('icon', 'fas fa-folder');

    fetch('<?= BASE_URL ?>/notification_center/createFolder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Folder created successfully!');
            loadEmailFolders(); // Reload folders
        } else {
            alert('Error creating folder: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error creating folder');
    });
}

function showCreateTemplateModal() {
    document.getElementById('templateModalTitle').textContent = 'Create Template';
    document.getElementById('templateForm').reset();
    document.getElementById('templateId').value = '';
    new bootstrap.Modal(document.getElementById('templateModal')).show();
}

function showEditTemplateModal(templateId, name, subject, body, category) {
    document.getElementById('templateModalTitle').textContent = 'Edit Template';
    document.getElementById('templateId').value = templateId;
    document.getElementById('templateName').value = name;
    document.getElementById('templateSubject').value = subject;
    document.getElementById('templateBody').value = body;
    document.getElementById('templateCategory').value = category;
    new bootstrap.Modal(document.getElementById('templateModal')).show();
}

function saveTemplateModal() {
    const formData = new FormData(document.getElementById('templateForm'));
    const templateId = document.getElementById('templateId').value;
    const url = templateId ? '<?= BASE_URL ?>/notification_center/updateTemplate' : '<?= BASE_URL ?>/notification_center/createTemplate';

    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide();
            loadEmailTemplates();
            showToast('success', data.message);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        showToast('error', 'Error saving template');
    });
}

function insertVariable(variable) {
    const textarea = document.getElementById('templateBody');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    textarea.value = text.substring(0, start) + variable + text.substring(end);
    textarea.focus();
    textarea.setSelectionRange(start + variable.length, start + variable.length);
}

function createTemplate(name, subject, body) {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('subject', subject);
    formData.append('body', body);
    formData.append('category', 'general');

    fetch('<?= BASE_URL ?>/notification_center/createTemplate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Template created successfully!');
            loadEmailTemplates(); // Reload templates
        } else {
            alert('Error creating template: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error creating template');
    });
}

function showReminderModal(messageId) {
    document.getElementById('reminderModalTitle').textContent = 'Set Reminder';
    document.getElementById('reminderForm').reset();
    document.getElementById('reminderMessageId').value = messageId;
    document.getElementById('reminderId').value = '';

    // Set default date to tomorrow at 9 AM
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(9, 0, 0, 0);
    document.getElementById('reminderDate').value = tomorrow.toISOString().slice(0, 16);

    new bootstrap.Modal(document.getElementById('reminderModal')).show();
}

function showEditReminderModal(reminderId, messageId, reminderDate, reminderText) {
    document.getElementById('reminderModalTitle').textContent = 'Edit Reminder';
    document.getElementById('reminderId').value = reminderId;
    document.getElementById('reminderMessageId').value = messageId;
    document.getElementById('reminderDate').value = reminderDate.slice(0, 16);
    document.getElementById('reminderText').value = reminderText;
    new bootstrap.Modal(document.getElementById('reminderModal')).show();
}

function saveReminderModal() {
    const formData = new FormData(document.getElementById('reminderForm'));
    const reminderId = document.getElementById('reminderId').value;
    const url = reminderId ? '<?= BASE_URL ?>/notification_center/updateReminder' : '<?= BASE_URL ?>/notification_center/setReminder';

    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('reminderModal')).hide();
            loadEmailReminders();
            showToast('success', data.message);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        showToast('error', 'Error saving reminder');
    });
}

function setQuickReminder(hours) {
    const now = new Date();
    now.setHours(now.getHours() + hours);
    document.getElementById('reminderDate').value = now.toISOString().slice(0, 16);
}

function setReminder(messageId, reminderDate) {
    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('reminder_date', reminderDate);
    formData.append('reminder_text', '');

    fetch('<?= BASE_URL ?>/notification_center/setReminder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Reminder set successfully!');
            loadEmailReminders(); // Reload reminders
        } else {
            alert('Error setting reminder: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error setting reminder');
    });
}

function showFolderSelectionModal(messageId) {
    document.getElementById('moveMessageId').value = messageId;

    // Load folders into modal
    fetch('<?= BASE_URL ?>/notification_center/getFolders')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                data.folders.forEach(folder => {
                    html += `
                        <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                                onclick="moveMessageToFolder(${messageId}, ${folder.id})">
                            <div>
                                <i class="${folder.icon}" style="color: ${folder.color}"></i>
                                <span class="ms-2">${folder.name}</span>
                            </div>
                            <span class="badge bg-secondary">${folder.message_count}</span>
                        </button>
                    `;
                });
                document.getElementById('folderList').innerHTML = html;
                new bootstrap.Modal(document.getElementById('moveToFolderModal')).show();
            }
        });
}

async function moveMessageToFolder(messageId, folderId) {
    try {
        // Get all message IDs in this conversation
        const response = await fetch(`<?= BASE_URL ?>/notification_center/getConversationMessageIds?message_id=${messageId}`);
        const conversationData = await response.json();
        
        if (!conversationData.success) {
            showToast('error', 'Failed to get conversation messages');
            return;
        }
        
        const allMessageIds = conversationData.message_ids || [messageId];
        
        // Move all messages in the conversation
        const formData = new FormData();
        formData.append('message_ids', allMessageIds.join(','));
        formData.append('folder_id', folderId);

        const moveResponse = await fetch('<?= BASE_URL ?>/notification_center/bulkMoveToFolder', {
            method: 'POST',
            body: formData
        });
        
        const data = await moveResponse.json();
        
        if (data.success) {
            // Close modal if open
            const modal = bootstrap.Modal.getInstance(document.getElementById('moveToFolderModal'));
            if (modal) modal.hide();

            showToast('success', `Conversation moved to folder successfully! (${allMessageIds.length} messages moved)`);

            // Refresh the current view
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast('error', 'Error moving conversation: ' + data.message);
        }
    } catch (error) {
        console.error('Error moving conversation:', error);
        showToast('error', 'Failed to move conversation');
    }
}

function showOwnershipTransferModal(messageId) {
    const newOwnerId = prompt('Enter new owner user ID:');
    if (newOwnerId) {
        transferOwnership(messageId, newOwnerId);
    }
}

function transferOwnership(messageId, newOwnerId) {
    // This would need a new controller method
    alert('Ownership transfer functionality needs to be implemented in controller');
}

// Bulk Operations Functions
let selectedEmails = new Set();

function toggleSelectAll(checkbox) {
    const emailCheckboxes = document.querySelectorAll('.email-checkbox');
    emailCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        if (checkbox.checked) {
            selectedEmails.add(cb.value);
        } else {
            selectedEmails.delete(cb.value);
        }
    });
    updateSelection();
}

function updateSelection() {
    selectedEmails.clear();
    document.querySelectorAll('.email-checkbox:checked').forEach(cb => {
        selectedEmails.add(cb.value);
    });

    const count = selectedEmails.size;
    const bulkBar = document.getElementById('bulk-actions-bar');
    const countSpan = document.getElementById('selected-count');

    if (bulkBar && countSpan) {
        if (count > 0) {
            bulkBar.classList.remove('d-none');
            countSpan.textContent = `${count} selected`;
        } else {
            bulkBar.classList.add('d-none');
        }
    }

    // Update select all checkbox
    const selectAllCheckbox = document.getElementById('select-all-emails');
    if (selectAllCheckbox) {
        const totalCheckboxes = document.querySelectorAll('.email-checkbox').length;
        selectAllCheckbox.checked = count === totalCheckboxes && count > 0;
        selectAllCheckbox.indeterminate = count > 0 && count < totalCheckboxes;
    }
}

function clearSelection() {
    selectedEmails.clear();
    document.querySelectorAll('.email-checkbox').forEach(cb => cb.checked = false);
    const selectAllCheckbox = document.getElementById('select-all-emails');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
    }
    updateSelection();
}

/**
 * Get all message IDs in selected conversations (including replies)
 * This is needed for bulk actions to work on entire conversation threads
 */
async function getAllMessageIdsInSelectedConversations() {
    if (selectedEmails.size === 0) {
        console.log('No emails selected');
        return [];
    }
    
    console.log('Getting message IDs for selected conversations:', Array.from(selectedEmails));
    const allMessageIds = [];
    
    // For each selected conversation root message, get all messages in that conversation
    for (const rootMessageId of selectedEmails) {
        try {
            console.log(`Fetching conversation IDs for message ${rootMessageId}`);
            const response = await fetch(`<?= BASE_URL ?>/notification_center/getConversationMessageIds?message_id=${rootMessageId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log(`Conversation data for message ${rootMessageId}:`, data);
            
            if (data.success && data.message_ids) {
                allMessageIds.push(...data.message_ids);
                console.log(`Added ${data.message_ids.length} message IDs for conversation`);
            } else {
                console.warn(`Failed to get conversation IDs for message ${rootMessageId}, using fallback`);
                // Fallback: if we can't get conversation IDs, just use the root message ID
                allMessageIds.push(parseInt(rootMessageId));
            }
        } catch (error) {
            console.error(`Error getting conversation message IDs for ${rootMessageId}:`, error);
            // Fallback: just use the root message ID
            allMessageIds.push(parseInt(rootMessageId));
        }
    }
    
    // Remove duplicates
    const uniqueIds = [...new Set(allMessageIds)];
    console.log('Final unique message IDs:', uniqueIds);
    return uniqueIds;
}

async function bulkMoveToFolder() {
    if (selectedEmails.size === 0) {
        showToast('error', 'Please select conversations to move');
        return;
    }

    // Show folder selection modal for bulk move
    fetch('<?= BASE_URL ?>/notification_center/getFolders')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="list-group">';
                data.folders.forEach(folder => {
                    html += `
                        <button type="button" class="list-group-item list-group-item-action" onclick="executeBulkMove(${folder.id})">
                            <i class="${folder.icon}" style="color: ${folder.color}"></i>
                            <span class="ms-2">${folder.name}</span>
                        </button>
                    `;
                });
                html += '</div>';

                // Create and show modal
                const modalHtml = `
                    <div class="modal fade" id="bulkMoveModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Move ${selectedEmails.size} emails to folder</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    ${html}
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHtml);
                new bootstrap.Modal(document.getElementById('bulkMoveModal')).show();
            }
        });
}

async function executeBulkMove(folderId) {
    try {
        // Get all message IDs in selected conversations
        const allMessageIds = await getAllMessageIdsInSelectedConversations();
        
        if (allMessageIds.length === 0) {
            showToast('error', 'No messages found in selected conversations');
            return;
        }

        const formData = new FormData();
        formData.append('message_ids', allMessageIds.join(','));
        formData.append('folder_id', folderId);

        const response = await fetch('<?= BASE_URL ?>/notification_center/bulkMoveToFolder', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        bootstrap.Modal.getInstance(document.getElementById('bulkMoveModal')).hide();
        showToast(data.success ? 'success' : 'error', data.message);
        
        if (data.success) {
            clearSelection();
            setTimeout(() => location.reload(), 1000);
        }
    } catch (error) {
        console.error('Error in bulk move:', error);
        showToast('error', 'Failed to move conversations');
        bootstrap.Modal.getInstance(document.getElementById('bulkMoveModal')).hide();
    }
}

async function bulkMarkAsRead() {
    if (selectedEmails.size === 0) {
        showToast('error', 'Please select conversations to mark as read');
        return;
    }

    if (!confirm(`Mark ${selectedEmails.size} conversation(s) as read? This will mark all messages in the selected conversations as read.`)) {
        return;
    }

    try {
        // Get all message IDs in selected conversations
        const allMessageIds = await getAllMessageIdsInSelectedConversations();
        
        if (allMessageIds.length === 0) {
            showToast('error', 'No messages found in selected conversations');
            return;
        }

        const formData = new FormData();
        formData.append('message_ids', allMessageIds.join(','));

        const response = await fetch('<?= BASE_URL ?>/notification_center/bulkMarkAsRead', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        showToast(data.success ? 'success' : 'error', data.message);
        if (data.success) {
            clearSelection();
            setTimeout(() => location.reload(), 1000);
        }
    } catch (error) {
        console.error('Error in bulk mark as read:', error);
        showToast('error', 'Failed to mark conversations as read');
    }
}

async function bulkArchive() {
    if (selectedEmails.size === 0) {
        showToast('error', 'Please select conversations to archive');
        return;
    }

    if (!confirm(`Archive ${selectedEmails.size} conversation(s)? This will archive all messages in the selected conversations.`)) {
        return;
    }

    try {
        // Get all message IDs in selected conversations
        const allMessageIds = await getAllMessageIdsInSelectedConversations();
        
        if (allMessageIds.length === 0) {
            showToast('error', 'No messages found in selected conversations');
            return;
        }

        const formData = new FormData();
        formData.append('message_ids', allMessageIds.join(','));

        const response = await fetch('<?= BASE_URL ?>/notification_center/bulkArchive', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        showToast(data.success ? 'success' : 'error', data.message);
        if (data.success) {
            clearSelection();
            setTimeout(() => location.reload(), 1000);
        }
    } catch (error) {
        console.error('Error in bulk archive:', error);
        showToast('error', 'Failed to archive conversations');
    }
}

// Email management bulk delete function (for manage tab only)
async function bulkDeleteEmails() {
    if (selectedEmails.size === 0) {
        showToast('error', 'Please select conversations to delete');
        return;
    }

    if (!confirm(`Permanently delete ${selectedEmails.size} conversation(s)? This will delete all messages in the selected conversations and cannot be undone.`)) {
        return;
    }

    try {
        // Get all message IDs in selected conversations
        const allMessageIds = await getAllMessageIdsInSelectedConversations();
        
        if (allMessageIds.length === 0) {
            showToast('error', 'No messages found in selected conversations');
            return;
        }

        const formData = new FormData();
        formData.append('message_ids', allMessageIds.join(','));

        const response = await fetch('<?= BASE_URL ?>/notification_center/bulkDelete', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        showToast(data.success ? 'success' : 'error', data.message);
        if (data.success) {
            clearSelection();
            setTimeout(() => location.reload(), 1000);
        }
    } catch (error) {
        console.error('Error in bulk delete:', error);
        showToast('error', 'Failed to delete conversations');
    }
}

function exportEmails() {
    // Show export modal with options
    const modalHtml = `
        <div class="modal fade" id="exportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Export Email Data</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="exportForm">
                            <div class="mb-3">
                                <label class="form-label">Export Format</label>
                                <div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="format" value="csv" id="formatCsv" checked>
                                        <label class="form-check-label" for="formatCsv">
                                            CSV (Excel compatible)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="format" value="json" id="formatJson">
                                        <label class="form-check-label" for="formatJson">
                                            JSON (Data processing)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="exportFolder" class="form-label">Folder Filter</label>
                                <select class="form-control" id="exportFolder" name="folder_id">
                                    <option value="">All Folders</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Date Range</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="date" class="form-control" name="start_date" placeholder="Start Date">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="date" class="form-control" name="end_date" placeholder="End Date">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="include_content" id="includeContent" checked>
                                    <label class="form-check-label" for="includeContent">
                                        Include message content
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="executeExport()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Load folders into dropdown
    fetch('<?= BASE_URL ?>/notification_center/getFolders')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('exportFolder');
                data.folders.forEach(folder => {
                    const option = document.createElement('option');
                    option.value = folder.id;
                    option.textContent = folder.name;
                    select.appendChild(option);
                });
            }
        });

    new bootstrap.Modal(document.getElementById('exportModal')).show();
}

function executeExport() {
    const form = document.getElementById('exportForm');
    const formData = new FormData(form);

    // Build query string
    const params = new URLSearchParams();
    for (const [key, value] of formData.entries()) {
        if (value) params.append(key, value);
    }

    const format = formData.get('format');
    const url = `<?= BASE_URL ?>/notification_center/exportEmails?${params.toString()}`;

    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();

    if (format === 'csv') {
        // Create download link for CSV
        const link = document.createElement('a');
        link.href = url;
        link.download = `emails_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        showToast('success', 'CSV export started - check your downloads');
    } else {
        // Show JSON in new window
        fetch(url)
            .then(response => response.json())
            .then(data => {
                const newWindow = window.open();
                newWindow.document.write(`
                    <html>
                        <head><title>Email Export - JSON</title></head>
                        <body>
                            <h3>Email Export Data</h3>
                            <pre style="white-space: pre-wrap; font-family: monospace;">${JSON.stringify(data, null, 2)}</pre>
                        </body>
                    </html>
                `);
                showToast('success', 'JSON export opened in new window');
            })
            .catch(error => {
                showToast('error', 'Export failed: ' + error.message);
            });
    }
}

function emailStatistics() {
    // Create enhanced statistics modal
    const modalHtml = `
        <div class="modal fade" id="statisticsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-chart-bar me-2"></i>Email Statistics & Analytics
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="statisticsContent">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">Loading comprehensive statistics...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary" onclick="exportStatistics()">
                            <i class="fas fa-download me-1"></i>Export Report
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    new bootstrap.Modal(document.getElementById('statisticsModal')).show();

    // Load statistics data
    fetch('<?= BASE_URL ?>/notification_center/getEmailStatistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayEnhancedStatistics(data);
            } else {
                document.getElementById('statisticsContent').innerHTML =
                    '<div class="alert alert-danger">Error loading statistics: ' + (data.message || 'Unknown error') + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('statisticsContent').innerHTML =
                '<div class="alert alert-danger">Network error loading statistics</div>';
        });
}

function displayEnhancedStatistics(data) {
    const stats = data.overall_stats;
    const folderStats = data.folder_stats;
    const templateStats = data.template_stats;
    const reminderStats = data.reminder_stats;

    let html = `
        <!-- Overview Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-2x mb-2"></i>
                        <h3>${stats.total_emails}</h3>
                        <p class="mb-0">Total Emails</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                        <h3>${stats.unread_emails}</h3>
                        <p class="mb-0">Unread</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3>${stats.follow_up_emails}</h3>
                        <p class="mb-0">Follow Up</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3>${stats.resolved_emails}</h3>
                        <p class="mb-0">Resolved</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Email Distribution by Folder</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="folderChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Email Activity Timeline</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="timelineChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Tables -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-folder me-2"></i>Folder Statistics</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Folder</th>
                                        <th>Total</th>
                                        <th>Unread</th>
                                        <th>%</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    const totalMessages = folderStats.reduce((sum, folder) => sum + folder.message_count, 0);
    folderStats.forEach(folder => {
        const percentage = totalMessages > 0 ? ((folder.message_count / totalMessages) * 100).toFixed(1) : 0;
        html += `
            <tr>
                <td>
                    <i class="${folder.icon}" style="color: ${folder.color}"></i>
                    <span class="ms-2">${folder.name}</span>
                </td>
                <td><span class="badge bg-secondary">${folder.message_count}</span></td>
                <td><span class="badge bg-warning">${folder.unread_count}</span></td>
                <td>${percentage}%</td>
            </tr>
        `;
    });

    html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>System Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                    <h4>${templateStats.total_templates}</h4>
                                    <small>Active Templates</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                                    <h4>${reminderStats.active_reminders}</h4>
                                    <small>Active Reminders</small>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-calendar-week fa-2x text-info mb-2"></i>
                                    <h4>${stats.this_week_emails}</h4>
                                    <small>This Week</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 border rounded">
                                    <i class="fas fa-calendar-alt fa-2x text-success mb-2"></i>
                                    <h4>${stats.this_month_emails}</h4>
                                    <small>This Month</small>
                                </div>
                            </div>
                        </div>

                        ${reminderStats.overdue_reminders > 0 ? `
                        <div class="alert alert-warning mt-3 mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>${reminderStats.overdue_reminders}</strong> overdue reminders need attention
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('statisticsContent').innerHTML = html;

    // Create charts after DOM is updated
    setTimeout(() => {
        createFolderChart(folderStats);
        createTimelineChart(stats);
    }, 100);
}

function createFolderChart(folderStats) {
    const ctx = document.getElementById('folderChart');
    if (!ctx) return;

    // Simple chart using CSS (since Chart.js might not be available)
    const total = folderStats.reduce((sum, folder) => sum + folder.message_count, 0);
    let chartHtml = '<div class="text-center">';

    folderStats.forEach(folder => {
        const percentage = total > 0 ? ((folder.message_count / total) * 100).toFixed(1) : 0;
        chartHtml += `
            <div class="mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="${folder.icon}" style="color: ${folder.color}"></i> ${folder.name}</span>
                    <span>${folder.message_count} (${percentage}%)</span>
                </div>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" style="width: ${percentage}%; background-color: ${folder.color}"></div>
                </div>
            </div>
        `;
    });

    chartHtml += '</div>';
    ctx.outerHTML = chartHtml;
}

function createTimelineChart(stats) {
    const ctx = document.getElementById('timelineChart');
    if (!ctx) return;

    // Simple timeline representation
    const timelineHtml = `
        <div class="text-center">
            <div class="row">
                <div class="col-4">
                    <div class="p-3 border rounded bg-light">
                        <h5 class="text-info">${stats.this_week_emails}</h5>
                        <small>This Week</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="p-3 border rounded bg-light">
                        <h5 class="text-success">${stats.this_month_emails}</h5>
                        <small>This Month</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="p-3 border rounded bg-light">
                        <h5 class="text-primary">${stats.total_emails}</h5>
                        <small>All Time</small>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">Email activity trends over time</small>
            </div>
        </div>
    `;

    ctx.outerHTML = timelineHtml;
}

function exportStatistics() {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `email_statistics_${timestamp}.json`;

    fetch('<?= BASE_URL ?>/notification_center/getEmailStatistics')
        .then(response => response.json())
        .then(data => {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            showToast('success', 'Statistics exported successfully');
        })
        .catch(error => {
            showToast('error', 'Export failed: ' + error.message);
        });
}

// Utility Functions
function deleteFolder(folderId) {
    if (confirm('Are you sure you want to delete this folder? All messages will be moved to Inbox.')) {
        const formData = new FormData();
        formData.append('folder_id', folderId);

        fetch('<?= BASE_URL ?>/notification_center/deleteFolder', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showToast(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                loadEmailFolders();
            }
        });
    }
}

function deleteTemplate(templateId) {
    if (confirm('Are you sure you want to delete this template?')) {
        const formData = new FormData();
        formData.append('template_id', templateId);

        fetch('<?= BASE_URL ?>/notification_center/deleteTemplate', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showToast(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                loadEmailTemplates();
            }
        });
    }
}

function completeReminder(reminderId) {
    const formData = new FormData();
    formData.append('reminder_id', reminderId);

    fetch('<?= BASE_URL ?>/notification_center/completeReminder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showToast(data.success ? 'success' : 'error', data.message);
        if (data.success) {
            loadEmailReminders();
        }
    });
}

function snoozeReminder(reminderId) {
    const hours = prompt('Snooze for how many hours?', '1');
    if (hours) {
        const formData = new FormData();
        formData.append('reminder_id', reminderId);
        formData.append('hours', hours);

        fetch('<?= BASE_URL ?>/notification_center/snoozeReminder', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showToast(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                loadEmailReminders();
            }
        });
    }
}

function deleteReminder(reminderId) {
    if (confirm('Are you sure you want to delete this reminder?')) {
        const formData = new FormData();
        formData.append('reminder_id', reminderId);

        fetch('<?= BASE_URL ?>/notification_center/deleteReminder', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showToast(data.success ? 'success' : 'error', data.message);
            if (data.success) {
                loadEmailReminders();
            }
        });
    }
}

function showToast(type, message) {
    // Create toast element
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // Add to toast container or create one
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // Add toast and show it
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// Load email management data when on management tab
document.addEventListener('DOMContentLoaded', function() {
    const currentStatus = '<?= $current_status ?>';
    const userRole = '<?= $user_role ?>';
    if (currentStatus === 'manage' && (userRole === 'admin' || userRole === 'coordinator')) {
        console.log('Loading email management data for ' + userRole + '...');
        loadEmailFolders();
        loadEmailTemplates();
        loadEmailReminders();
        // Don't load statistics - use PHP-generated counts
    }
});

function loadEmailTemplates() {
    console.log('Loading email templates...');
    fetch('<?= BASE_URL ?>/notification_center/getEmailTemplates')
        .then(response => {
            console.log('Templates response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Templates data:', data);
            if (data.success) {
                displayEmailTemplates(data.templates);
            } else {
                console.error('Error loading templates:', data.message);
                document.getElementById('email-templates-list').innerHTML = '<div class="text-center p-3 text-danger">Error: ' + (data.message || 'Unknown error') + '</div>';
            }
        })
        .catch(error => {
            console.error('Fetch error loading templates:', error);
            document.getElementById('email-templates-list').innerHTML = '<div class="text-center p-3 text-danger">Network error loading templates: ' + error.message + '</div>';
        });
}

function displayEmailTemplates(templates) {
    let html = '';
    templates.forEach(template => {
        html += `
            <div class="list-group-item py-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="small fw-bold">${template.name}</div>
                        <div class="text-muted" style="font-size: 11px;">${template.category}</div>
                        <div class="text-muted" style="font-size: 10px;">${template.subject.substring(0, 40)}...</div>
                    </div>
                    <div class="btn-group btn-group-sm ms-2">
                        <button class="btn btn-outline-primary btn-sm p-1" onclick="showEditTemplateModal(${template.id}, '${template.name}', '${template.subject}', \`${template.body}\`, '${template.category}')" title="Edit" style="font-size: 10px;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm p-1" onclick="deleteTemplate(${template.id})" title="Delete" style="font-size: 10px;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    document.getElementById('email-templates-list').innerHTML = html;
}

function loadEmailReminders() {
    console.log('Loading email reminders...');
    fetch('<?= BASE_URL ?>/notification_center/getReminders')
        .then(response => {
            console.log('Reminders response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Reminders data:', data);
            if (data.success) {
                displayEmailReminders(data.reminders);
            } else {
                console.error('Error loading reminders:', data.message);
                document.getElementById('email-reminders-list').innerHTML = '<div class="text-center p-3 text-danger">Error: ' + (data.message || 'Unknown error') + '</div>';
            }
        })
        .catch(error => {
            console.error('Fetch error loading reminders:', error);
            document.getElementById('email-reminders-list').innerHTML = '<div class="text-center p-3 text-danger">Network error loading reminders: ' + error.message + '</div>';
        });
}

function displayEmailReminders(reminders) {
    let html = '';
    if (reminders.length === 0) {
        html = '<div class="text-center p-3 text-muted"><i class="fas fa-bell-slash"></i> No active reminders</div>';
    } else {
        reminders.forEach(reminder => {
            const isOverdue = new Date(reminder.reminder_date) < new Date();
            const reminderDate = new Date(reminder.reminder_date).toLocaleDateString();
            html += `
                <div class="list-group-item py-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="small fw-bold">${reminder.message_subject.substring(0, 30)}...</div>
                            <div class="text-muted" style="font-size: 11px;">${reminderDate}</div>
                            ${reminder.reminder_text ? `<div class="text-info" style="font-size: 10px;">${reminder.reminder_text.substring(0, 40)}...</div>` : ''}
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <span class="badge bg-${isOverdue ? 'danger' : 'warning'} mb-1" style="font-size: 9px;">${isOverdue ? 'Overdue' : 'Pending'}</span>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-success btn-sm p-1" onclick="completeReminder(${reminder.id})" title="Complete" style="font-size: 9px;">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm p-1" onclick="snoozeReminder(${reminder.id})" title="Snooze" style="font-size: 9px;">
                                    <i class="fas fa-clock"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    document.getElementById('email-reminders-list').innerHTML = html;
}

function loadEmailStatistics() {
    // Don't override the PHP-generated counts - they're already correct
    console.log('Email statistics already loaded from PHP');
}

// Show-Specific Organization Functions
function filterByShow() {
    const userRole = '<?= $user_role ?>';
    let showId = '';

    if (userRole === 'admin') {
        // Admin uses search input
        const selectedShow = document.getElementById('show-search').dataset.selectedShowId;
        showId = selectedShow || '';
    } else {
        // Coordinator uses dropdown
        showId = document.getElementById('coordinator-show-filter').value;
    }

    refreshEmailList(showId);
}

// Admin search functionality for thousands of shows
let searchTimeout;
let currentSearchTerm = '';

function searchShows(searchTerm) {
    currentSearchTerm = searchTerm.trim();

    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // Debounce search to avoid too many requests
    searchTimeout = setTimeout(() => {
        if (currentSearchTerm.length >= 2) {
            performShowSearch(currentSearchTerm);
        } else if (currentSearchTerm.length === 0) {
            hideSearchResults();
        }
    }, 300);
}

function performShowSearch(searchTerm) {
    fetch(`<?= BASE_URL ?>/notification_center/searchShows?q=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySearchResults(data.shows);
            } else {
                showToast('error', 'Error searching shows');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
        });
}

function displaySearchResults(shows) {
    const resultsDiv = document.getElementById('show-search-results');

    if (shows.length === 0) {
        resultsDiv.innerHTML = '<div class="p-2 text-muted">No shows found</div>';
    } else {
        let html = '';
        shows.forEach(show => {
            const statusBadge = show.status === 'completed' ? ' <small class="text-muted">(Completed)</small>' : '';
            html += `
                <div class="p-2 border-bottom show-search-item"
                     onclick="selectShow(${show.id}, '${show.name.replace(/'/g, "\\'")}', '${show.status}')"
                     style="cursor: pointer;">
                    <div class="fw-bold">${show.name}${statusBadge}</div>
                    <small class="text-muted">${show.start_date} - ${show.end_date}</small>
                </div>
            `;
        });
        resultsDiv.innerHTML = html;
    }

    resultsDiv.classList.remove('d-none');
}

function selectShow(showId, showName, showStatus) {
    const searchInput = document.getElementById('show-search');
    const statusBadge = showStatus === 'completed' ? ' (Completed)' : '';

    searchInput.value = showName + statusBadge;
    searchInput.dataset.selectedShowId = showId;

    hideSearchResults();
    refreshEmailList(showId);
}

function showSearchResults() {
    if (currentSearchTerm.length >= 2) {
        const searchResults = document.getElementById('show-search-results');
        if (searchResults) {
            searchResults.classList.remove('d-none');
        }
    }
}

function hideSearchResults() {
    const searchResults = document.getElementById('show-search-results');
    if (searchResults) {
        searchResults.classList.add('d-none');
    }
}

function clearShowFilter() {
    const userRole = '<?= $user_role ?>';

    if (userRole === 'admin') {
        const showSearch = document.getElementById('show-search');
        if (showSearch) {
            showSearch.value = '';
            showSearch.dataset.selectedShowId = '';
        }
        hideSearchResults();
    } else {
        const coordinatorFilter = document.getElementById('coordinator-show-filter');
        if (coordinatorFilter) {
            coordinatorFilter.value = '';
        }
    }

    refreshEmailList('');
}

// Hide search results when clicking outside
document.addEventListener('click', function(event) {
    const searchContainer = document.querySelector('.position-relative');
    if (searchContainer && !searchContainer.contains(event.target)) {
        hideSearchResults();
    }
});

function showRecentShows() {
    const userRole = '<?= $user_role ?>';

    if (userRole === 'admin') {
        // For admin, show recent shows they've accessed
        fetch('<?= BASE_URL ?>/notification_center/getRecentShows')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data.shows);
                    document.getElementById('show-search-results').classList.remove('d-none');
                } else {
                    showToast('error', 'Error loading recent shows');
                }
            });
    } else {
        // For coordinator, just show their dropdown
        document.getElementById('coordinator-show-filter').focus();
    }
}



// Format UTC timestamp for user's timezone (handle timing issues)
function formatUTCTimestampForUser(utcTimestamp) {
    if (!utcTimestamp) return 'Unknown';

    try {


        // Check if TimezoneHelper is available (use the method that actually exists)
        if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTimeAsUTC) {
            const utcDate = window.TimezoneHelper.parseMySQLDateTimeAsUTC(utcTimestamp);

            if (utcDate) {
                // Use browser's toLocaleDateString which works reliably
                return utcDate.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
            }
        }

        // Fallback: Manual UTC parsing
        const [datePart, timePart] = utcTimestamp.split(' ');
        const [year, month, day] = datePart.split('-');
        const [hour, minute, second] = timePart.split(':');

        // Create date in UTC timezone (JavaScript months are 0-based)
        const utcDate = new Date(Date.UTC(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
            parseInt(hour),
            parseInt(minute),
            parseInt(second) || 0
        ));

        // Use browser's toLocaleDateString for consistent formatting
        return utcDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

    } catch (error) {
        console.error('Error formatting UTC timestamp:', error);
        return 'Invalid Date';
    }
}

// Force Email Check Function (Admin Only)
function forceEmailCheck() {
    const button = document.getElementById('force-email-btn');
    const originalText = button.innerHTML;

    // Disable button and show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

    showToast('info', 'Forcing email check - this may take a few seconds...');

    fetch('<?= BASE_URL ?>/notification_center/forceEmailCheck', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);

            // Wait a moment then refresh the page to show new emails
            setTimeout(() => {
                showToast('info', 'Refreshing page to show new emails...');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }, 2000);
        } else {
            showToast('error', 'Email check failed: ' + data.message);

            // Re-enable button
            button.disabled = false;
            button.innerHTML = originalText;
        }
    })
    .catch(error => {
        showToast('error', 'Network error during email check');

        // Re-enable button
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function refreshEmailList(showId = '') {
    const params = new URLSearchParams();
    if (showId) params.append('show_id', showId);

    fetch(`<?= BASE_URL ?>/notification_center/getEmailsByShow?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateEmailTable(data.emails);
                showToast('success', `Loaded ${data.emails.length} emails`);
            } else {
                showToast('error', 'Error loading emails: ' + data.message);
            }
        })
        .catch(error => {
            showToast('error', 'Network error loading emails');
        });
}

function updateEmailTable(emails) {
    console.log('[EMAIL-TABLE] 📧 Updating email table with', emails.length, 'emails');
    console.log('[EMAIL-TABLE] 🔍 TimezoneHelper status:', !!window.TimezoneHelper);
    const tbody = document.querySelector('table tbody');
    if (!tbody) return;

    if (emails.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-3">
                    No email messages found for the selected filter.
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    emails.forEach(conversation => {
        // Handle both individual emails and grouped conversations
        const rootMessage = conversation.root_message || conversation;
        const totalMessages = conversation.total_messages || 1;
        const hasUnread = conversation.has_unread || (rootMessage.is_read === 0);
        const lastActivity = conversation.last_activity || rootMessage.created_at;
        
        // Safely get subject with fallback
        const subject = rootMessage.subject || 'No Subject';
        const displaySubject = subject.length > 40 ? subject.substring(0, 40) + '...' : subject;
        
        html += `
            <tr class="${hasUnread ? 'table-warning' : ''}">
                <td>
                    <input type="checkbox" class="email-checkbox" value="${rootMessage.id}" onchange="updateSelection()">
                </td>
                <td>
                    <a href="<?= BASE_URL ?>/notification_center/viewMessage/${rootMessage.id}" class="text-decoration-none">
                        <div class="fw-bold text-primary" style="font-size: 13px;">
                            ${displaySubject}
                            ${totalMessages > 1 ? `<span class="badge bg-secondary ms-1" style="font-size: 10px;">${totalMessages}</span>` : ''}
                        </div>
                        ${rootMessage.show_title ? `
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-car me-1"></i>Show:
                                    <a href="<?= BASE_URL ?>/show/view/${rootMessage.show_id}"
                                       class="text-decoration-none text-primary"
                                       title="View show details">
                                        ${rootMessage.show_title}
                                    </a>
                                    ${rootMessage.show_location ? `• ${rootMessage.show_location}` : ''}
                                </small>
                            </div>
                        ` : ''}
                    </a>
                </td>
                <td>
                    <small>${rootMessage.original_sender_email || rootMessage.sender_email || 'System'}</small>
                </td>
                <td>
                    <small>${formatUTCTimestampForUser(lastActivity)}</small>
                </td>
                <td>
                    <span class="badge" style="background-color: ${rootMessage.folder_color || '#007bff'}; font-size: 10px;">
                        ${rootMessage.folder_name || 'Inbox'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary btn-sm p-1" onclick="showFolderSelectionModal(${rootMessage.id})" title="Move to Folder" style="font-size: 10px;">
                            <i class="fas fa-folder"></i>
                        </button>
                        <button class="btn btn-outline-warning btn-sm p-1" onclick="showReminderModal(${rootMessage.id})" title="Set Reminder" style="font-size: 10px;">
                            <i class="fas fa-bell"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
    clearSelection(); // Clear any previous selections
}

// Simple email table sorting
let emailSortDirection = {};
function sortEmailTable(column) {
    const tbody = document.querySelector('table tbody');
    if (!tbody) return;

    const rows = Array.from(tbody.querySelectorAll('tr')).filter(row => !row.querySelector('td[colspan]'));
    if (rows.length === 0) return;

    // Toggle sort direction
    emailSortDirection[column] = emailSortDirection[column] === 'asc' ? 'desc' : 'asc';
    const direction = emailSortDirection[column];

    // Update sort icons
    document.querySelectorAll('thead th i.fas').forEach(icon => {
        icon.className = 'fas fa-sort text-muted';
    });
    const currentIcon = document.querySelector(`thead th[onclick*="${column}"] i`);
    if (currentIcon) {
        currentIcon.className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'} text-primary`;
    }

    // Sort rows
    rows.sort((a, b) => {
        let aVal, bVal;

        switch(column) {
            case 'subject':
                aVal = a.cells[1].textContent.trim().toLowerCase();
                bVal = b.cells[1].textContent.trim().toLowerCase();
                break;
            case 'from':
                aVal = a.cells[2].textContent.trim().toLowerCase();
                bVal = b.cells[2].textContent.trim().toLowerCase();
                break;
            case 'date':
                // Extract date text and convert to comparable format
                aVal = new Date(a.cells[3].textContent.trim());
                bVal = new Date(b.cells[3].textContent.trim());
                break;
            default:
                return 0;
        }

        if (column === 'date') {
            return direction === 'asc' ? aVal - bVal : bVal - aVal;
        } else {
            if (aVal < bVal) return direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return direction === 'asc' ? 1 : -1;
            return 0;
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}


</script>

<!-- Timezone Helper - MUST be loaded BEFORE other scripts (same as calendar) -->
<script src="<?php echo URLROOT; ?>/public/js/timezone-helper.js?v=<?php echo filemtime(APPROOT . '/public/js/timezone-helper.js'); ?>"></script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>