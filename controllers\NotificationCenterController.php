<?php
/**
 * Notification Center Controller
 * 
 * This controller handles the notification center functionality including
 * viewing notifications, messages, and handling replies.
 */
class NotificationCenterController extends Controller {
    private $auth;
    private $db;
    private $notificationCenterModel;
    private $userModel;
    private $showModel;
    private $emailFolderModel;
    private $emailTemplateModel;
    private $emailReminderModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Call parent constructor first
        parent::__construct();

        // Initialize core dependencies
        $this->auth = new Auth();
        $this->db = new Database();
        
        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Initialize models - use unified message system
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        require_once APPROOT . '/models/EmailService.php';
        require_once APPROOT . '/models/SettingsModel.php';
        $this->notificationCenterModel = new UnifiedMessageModel();
        $this->userModel = $this->model('UserModel');

        // Initialize email management models
        require_once APPROOT . '/models/EmailFolderModel.php';
        require_once APPROOT . '/models/EmailTemplateModel.php';
        require_once APPROOT . '/models/EmailReminderModel.php';

        $this->emailFolderModel = new EmailFolderModel();
        $this->emailTemplateModel = new EmailTemplateModel();
        $this->emailReminderModel = new EmailReminderModel();
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Default index method - shows notification center
     */
    public function index() {
        $this->center();
    }
    
    /**
     * Main notification center page - unified messages view
     */
    public function center() {
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();

        // Check if user can use notification system
        $canSendNotifications = $this->notificationCenterModel->canUserSendNotifications($userId);

        // Get filter parameters with email support for admin/coordinator
        $status = $_GET['status'] ?? 'all';
        $validStatuses = ['all', 'unread', 'archived'];

        // Add email status for admin/coordinator roles
        if (in_array($userRole, ['admin', 'coordinator'])) {
            $validStatuses[] = 'email';
        }

        // Add email management status for admin only
        if ($userRole === 'admin') {
            $validStatuses[] = 'manage';
        }

        if (!in_array($status, $validStatuses)) {
            $status = 'all';
        }

        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // Get messages based on status and role
        if ($status === 'email' && in_array($userRole, ['admin', 'coordinator'])) {
            $messages = $this->getEmailMessages($userId, $userRole, $limit, $offset);
        } elseif ($status === 'manage' && $userRole === 'admin') {
            // For email management, we don't need to load messages initially
            $messages = [];
        } else {
            $messages = $this->notificationCenterModel->getUserMessages(
                $userId,
                $status,
                $limit,
                $offset
            );
        }

        // Group messages into conversations based on ticket numbers
        if (!empty($messages) && $status !== 'manage') {
            // Use actual counts for email tabs to match "all messages" tab behavior
            $useActualCounts = ($status === 'email');
            $messages = $this->groupMessagesByTicketNumber($messages, $useActualCounts);
        }

        // Get counts for badges - use the reliable database counts
        $counts = $this->notificationCenterModel->getMessageCounts($userId);

        // Add email counts for admin/coordinator
        if (in_array($userRole, ['admin', 'coordinator'])) {
            $counts['email_count'] = $this->getEmailConversationCount($userId, $userRole);
            // For manage tab, use the same count as email tab (total email conversations)
            $counts['manage_count'] = $counts['email_count'];
        }

        // Get conversation counts for tabs
        $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);

        // Calculate pagination
        $totalCount = $counts['total_count'];
        if ($status === 'unread') {
            $totalCount = $counts['total_unread'];
        } elseif ($status === 'archived') {
            $totalCount = $counts['archived_count'];
        } elseif ($status === 'email') {
            $totalCount = $counts['email_count'] ?? 0;
        } elseif ($status === 'manage') {
            $totalCount = $counts['manage_count'] ?? 0;
        }

        $totalPages = (int)ceil($totalCount / $limit);

        $data = [
            'title' => 'Messages',
            'messages' => $messages,
            'counts' => $counts,
            'conversationCounts' => $conversationCounts,
            'current_status' => $status,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount,
            'can_send_notifications' => $canSendNotifications,
            'user_id' => $userId,
            'user_role' => $userRole,
            'has_email_access' => in_array($userRole, ['admin', 'coordinator'])
        ];

        // Add email management data if on manage tab (for admin and coordinator)
        if ($status === 'manage' && in_array($userRole, ['admin', 'coordinator'])) {
            // Get email statistics for dashboard - count conversations, not individual messages
            $this->db->query("SELECT COUNT(DISTINCT CASE WHEN ticket_number IS NOT NULL THEN ticket_number ELSE id END) as total_emails 
                             FROM messages 
                             WHERE message_type = 'email' AND is_archived = 0");
            $totalEmails = $this->db->single();

            $this->db->query("SELECT COUNT(DISTINCT CASE WHEN ticket_number IS NOT NULL THEN ticket_number ELSE id END) as follow_up 
                             FROM messages 
                             WHERE message_type = 'email' AND folder_id = 3 AND is_archived = 0");
            $followUpEmails = $this->db->single();

            $this->db->query("SELECT COUNT(DISTINCT CASE WHEN ticket_number IS NOT NULL THEN ticket_number ELSE id END) as resolved 
                             FROM messages 
                             WHERE message_type = 'email' AND folder_id = 4 AND is_archived = 0");
            $resolvedEmails = $this->db->single();

            // Get recent emails for dashboard
            if ($userRole === 'admin') {
                // Admin can see all emails
                $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color
                                 FROM messages m
                                 LEFT JOIN email_folders f ON m.folder_id = f.id
                                 WHERE m.message_type = 'email' AND m.is_archived = 0
                                 ORDER BY m.created_at DESC
                                 LIMIT 50");  // Get more messages to allow for proper grouping
                $recentEmailsRaw = $this->db->resultSet();
            } else {
                // Coordinator can see all emails for now (until show_roles table is implemented)
                $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color
                                 FROM messages m
                                 LEFT JOIN email_folders f ON m.folder_id = f.id
                                 WHERE m.message_type = 'email' AND m.is_archived = 0
                                 ORDER BY m.created_at DESC
                                 LIMIT 50");  // Get more messages to allow for proper grouping
                $recentEmailsRaw = $this->db->resultSet();
            }
            
            // Group recent emails by ticket number (use actual counts for email management)
            $recentEmails = $this->groupMessagesByTicketNumber($recentEmailsRaw, true);
            
            // Limit to 10 conversations after grouping
            $recentEmails = array_slice($recentEmails, 0, 10);

            $data['email_stats'] = [
                'total_emails' => $totalEmails ? $totalEmails->total_emails : 0,
                'follow_up' => $followUpEmails ? $followUpEmails->follow_up : 0,
                'resolved' => $resolvedEmails ? $resolvedEmails->resolved : 0
            ];
            $data['recent_emails'] = $recentEmails;
            $data['email_folders'] = $this->emailFolderModel->getAllFolders();
            $data['email_templates'] = $this->emailTemplateModel->getAllTemplates(true);
            $data['email_reminders'] = $this->emailReminderModel->getUserReminders($userId);
        }

        parent::view('notification_center/index', $data);
    }

    /**
     * Get email messages for admin/coordinator roles
     */
    private function getEmailMessages($userId, $userRole, $limit, $offset) {
        $sql = "SELECT m.*, u.name as from_user_name, s.name as show_title, s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE m.message_type = 'email'
                AND m.is_archived = 0";

        // Role-based filtering
        if ($userRole === 'coordinator') {
            // Coordinators only see emails for their shows
            $sql .= " AND (m.ticket_number LIKE 'RER-C%'
                     AND m.show_id IN (
                         SELECT show_id FROM show_role_assignments
                         WHERE user_id = :user_id AND role = 'coordinator'
                     ))";

            $sql .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        } elseif ($userRole === 'admin') {
            // Admins see all email messages - use separate parameters for each occurrence
            $sql .= " AND (m.to_user_id = :user_id1 OR m.from_user_id = :user_id2 OR m.owned_by_admin_id = :user_id3 OR m.owned_by_admin_id IS NULL)";
            $sql .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";

            $this->db->query($sql);
            $this->db->bind(':user_id1', $userId);
            $this->db->bind(':user_id2', $userId);
            $this->db->bind(':user_id3', $userId);
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        } else {
            // Other roles don't have email access
            return [];
        }

        return $this->db->resultSet();
    }

    /**
     * Get email message count for admin/coordinator roles
     */
    private function getEmailMessageCount($userId, $userRole) {
        $sql = "SELECT COUNT(*) as count FROM messages m
                WHERE m.message_type = 'email'
                AND m.is_archived = 0";

        // Role-based filtering
        if ($userRole === 'coordinator') {
            // Coordinators only see emails for their shows
            $sql .= " AND (m.ticket_number LIKE 'RER-C%'
                     AND m.show_id IN (
                         SELECT show_id FROM show_role_assignments
                         WHERE user_id = :user_id AND role = 'coordinator'
                     ))";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
        } elseif ($userRole === 'admin') {
            // Admins see all email messages - use separate parameters for each occurrence
            $sql .= " AND (m.to_user_id = :user_id1 OR m.from_user_id = :user_id2 OR m.owned_by_admin_id = :user_id3 OR m.owned_by_admin_id IS NULL)";

            $this->db->query($sql);
            $this->db->bind(':user_id1', $userId);
            $this->db->bind(':user_id2', $userId);
            $this->db->bind(':user_id3', $userId);
        } else {
            // Other roles don't have email access
            return 0;
        }

        $result = $this->db->single();

        return $result ? (int)$result->count : 0;
    }

    /**
     * Get email conversation count for admin/coordinator roles (counts conversations, not individual messages)
     */
    private function getEmailConversationCount($userId, $userRole) {
        // Count distinct conversations (by ticket number) for email messages
        $sql = "SELECT COUNT(DISTINCT CASE WHEN m.ticket_number IS NOT NULL THEN m.ticket_number ELSE m.id END) as count 
                FROM messages m
                WHERE m.message_type = 'email'
                AND m.is_archived = 0";

        // Role-based filtering
        if ($userRole === 'coordinator') {
            // Coordinators only see emails for their shows
            $sql .= " AND (m.ticket_number LIKE 'RER-C%'
                     AND m.show_id IN (
                         SELECT show_id FROM show_role_assignments
                         WHERE user_id = :user_id AND role = 'coordinator'
                     ))";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
        } elseif ($userRole === 'admin') {
            // Admins see all email messages - use separate parameters for each occurrence
            $sql .= " AND (m.to_user_id = :user_id1 OR m.from_user_id = :user_id2 OR m.owned_by_admin_id = :user_id3 OR m.owned_by_admin_id IS NULL)";

            $this->db->query($sql);
            $this->db->bind(':user_id1', $userId);
            $this->db->bind(':user_id2', $userId);
            $this->db->bind(':user_id3', $userId);
        } else {
            // Other roles don't have email access
            return 0;
        }

        $result = $this->db->single();
        return $result ? (int)$result->count : 0;
    }

    /**
     * Group messages by ticket number for threaded display
     */
    private function groupMessagesByTicketNumber($messages, $useActualCounts = false) {
        $conversations = [];
        $processedMessages = [];
        
        foreach ($messages as $message) {
            // Skip if already processed
            if (in_array($message->id, $processedMessages)) {
                continue;
            }
            
            // Extract ticket number from subject or use ticket_number field
            $ticketNumber = $this->extractTicketNumber($message);
            
            if (!$ticketNumber) {
                // Check if this is part of a parent_message_id thread (non-email messages)
                $threadKey = $this->getThreadKey($message, $messages);
                
                if ($threadKey && !isset($conversations[$threadKey])) {
                    // Create conversation for parent_message_id thread
                    $threadMessages = $this->getThreadMessages($message, $messages);
                    $rootMessage = $this->findRootMessage($threadMessages);
                    
                    $conversations[$threadKey] = [
                        'root_message' => $rootMessage,
                        'replies' => array_filter($threadMessages, function($m) use ($rootMessage) {
                            return $m->id !== $rootMessage->id;
                        }),
                        'total_messages' => count($threadMessages),
                        'last_activity' => max(array_map(function($m) { return $m->created_at; }, $threadMessages)),
                        'has_unread' => !empty(array_filter($threadMessages, function($m) { return !$m->is_read; })),
                        'ticket_number' => null
                    ];
                    
                    // Mark all thread messages as processed
                    foreach ($threadMessages as $threadMsg) {
                        $processedMessages[] = $threadMsg->id;
                    }
                } elseif (!$threadKey) {
                    // Single message with no threading
                    $conversations[] = [
                        'root_message' => $message,
                        'replies' => [],
                        'total_messages' => 1,
                        'last_activity' => $message->created_at,
                        'has_unread' => !$message->is_read,
                        'ticket_number' => null
                    ];
                    $processedMessages[] = $message->id;
                }
                continue;
            }
            
            // Find or create conversation for this ticket
            if (!isset($conversations[$ticketNumber])) {
                $totalMessages = 1; // Default count
                
                // For email management tab, get actual count from database to handle folder filtering
                if ($useActualCounts) {
                    // Count ALL messages in conversation (not just emails) to match "all messages" tab behavior
                    $this->db->query("SELECT COUNT(*) as total FROM messages WHERE ticket_number = ? AND is_archived = 0");
                    $this->db->bind(1, $ticketNumber);
                    $countResult = $this->db->single();
                    $totalMessages = $countResult ? (int)$countResult->total : 1;
                }
                
                $conversations[$ticketNumber] = [
                    'root_message' => $message,
                    'replies' => [],
                    'total_messages' => $totalMessages,
                    'last_activity' => $message->created_at,
                    'has_unread' => !$message->is_read,
                    'ticket_number' => $ticketNumber
                ];
                $processedMessages[] = $message->id;
            } else {
                // Add as reply to existing conversation
                $conversations[$ticketNumber]['replies'][] = $message;
                
                // Only increment count if not using actual counts
                if (!$useActualCounts) {
                    $conversations[$ticketNumber]['total_messages']++;
                }
                
                // Update last activity if this message is newer
                if ($message->created_at > $conversations[$ticketNumber]['last_activity']) {
                    $conversations[$ticketNumber]['last_activity'] = $message->created_at;
                }
                
                // Update unread status
                if (!$message->is_read) {
                    $conversations[$ticketNumber]['has_unread'] = true;
                }
                
                $processedMessages[] = $message->id;
            }
        }
        
        // Sort conversations by last activity (newest first)
        uasort($conversations, function($a, $b) {
            return strtotime($b['last_activity']) - strtotime($a['last_activity']);
        });
        
        return array_values($conversations);
    }
    
    /**
     * Extract ticket number from message subject or ticket_number field
     */
    private function extractTicketNumber($message) {
        // First check if ticket_number field exists and has a value
        if (isset($message->ticket_number) && !empty($message->ticket_number)) {
            return $message->ticket_number;
        }

        // Extract from subject line using regex
        $subject = $message->subject ?? '';

        // Use simplified extraction pattern for both formats
        // Matches: [RER-2025-001-ZIK2WU], [RER-A25-01-001-ZIK2WU], [RER-C25-01-001-ZIK2WU]
        // Also matches: [RER-2025-001], [RER-A25-01-001], [RER-C25-01-001] (for backward compatibility)
        if (preg_match('/\[(RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6}|\d{4}-\d+|[AC]\d{2}-\d+-\d+))\]/', $subject, $matches)) {
            return $matches[1];
        }

        // Match unbracketed patterns as fallback
        if (preg_match('/(RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6}|\d{4}-\d+|[AC]\d{2}-\d+-\d+))/', $subject, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Get thread key for parent_message_id threading
     */
    private function getThreadKey($message, $messages) {
        // If message has parent_message_id, use that as thread key
        if (!empty($message->parent_message_id)) {
            return 'thread_' . $message->parent_message_id;
        }
        
        // If message is a parent (other messages reference it), use its ID as thread key
        foreach ($messages as $otherMessage) {
            if (!empty($otherMessage->parent_message_id) && $otherMessage->parent_message_id == $message->id) {
                return 'thread_' . $message->id;
            }
        }
        
        return null;
    }

    /**
     * Get all messages in a parent_message_id thread
     */
    private function getThreadMessages($message, $messages) {
        $threadMessages = [];
        $parentId = !empty($message->parent_message_id) ? $message->parent_message_id : $message->id;
        
        foreach ($messages as $msg) {
            // Include parent message and all replies
            if ($msg->id == $parentId || $msg->parent_message_id == $parentId) {
                $threadMessages[] = $msg;
            }
        }
        
        return $threadMessages;
    }

    /**
     * Find root message in a thread (message with no parent_message_id)
     */
    private function findRootMessage($threadMessages) {
        foreach ($threadMessages as $message) {
            if (empty($message->parent_message_id)) {
                return $message;
            }
        }
        
        // Fallback: return first message if no clear root found
        return $threadMessages[0] ?? null;
    }

    /**
     * Get email templates for admin/coordinator quick replies
     */
    public function getEmailTemplates() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();

        // Only admin and coordinator can access email templates
        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        try {
            // Get active email templates
            $this->db->query("SELECT id, name, subject, body, template_variables, category FROM email_templates WHERE is_active = 1 ORDER BY category ASC, name ASC");
            $templates = $this->db->resultSet();

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'templates' => $templates]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error loading templates']);
        }
    }

    /**
     * Send email reply using template
     */
    public function sendEmailReply() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();

        // Only admin and coordinator can send email replies
        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageId = (int)($_POST['message_id'] ?? 0);
        $subject = $_POST['subject'] ?? '';
        $message = $_POST['message'] ?? '';
        $templateId = (int)($_POST['template_id'] ?? 0);

        if (!$messageId || (!$message && !$templateId)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message ID and content are required']);
            return;
        }

        try {
            // Get original message
            $this->db->query("SELECT * FROM messages WHERE id = :message_id");
            $this->db->bind(':message_id', $messageId);
            $originalMessage = $this->db->single();

            if (!$originalMessage) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Original message not found']);
                return;
            }

            // If template is used, load template content
            if ($templateId) {
                $this->db->query("SELECT subject, body FROM email_templates WHERE id = :template_id AND is_active = 1");
                $this->db->bind(':template_id', $templateId);
                $template = $this->db->single();

                if ($template) {
                    $subject = $template->subject;
                    $message = $template->body;

                    // Get current user name
                    $this->db->query("SELECT name FROM users WHERE id = :user_id");
                    $this->db->bind(':user_id', $userId);
                    $currentUser = $this->db->single();
                    $adminName = $currentUser ? $currentUser->name : 'Admin';

                    // Replace template variables
                    $replacements = [
                        '{{subject}}' => $originalMessage->subject,
                        '{{ticket_number}}' => $originalMessage->ticket_number ?? '',
                        '{{date}}' => date('Y-m-d H:i:s'),
                        '{{site_name}}' => 'Events and Shows Platform',
                        '{{admin_name}}' => $adminName
                    ];

                    foreach ($replacements as $variable => $value) {
                        $subject = str_replace($variable, $value, $subject);
                        $message = str_replace($variable, $value, $message);
                    }
                }
            }

            // Create email reply using UnifiedMessageModel
            require_once APPROOT . '/models/UnifiedMessageModel.php';
            $unifiedMessageModel = new UnifiedMessageModel();

            $replyId = $unifiedMessageModel->createEmailReply(
                $userId,
                $messageId,
                $subject,
                $message,
                $originalMessage->original_sender_email
            );

            if ($replyId) {
                // Send actual email to original sender if they have an email
                $success = true;
                if (!empty($originalMessage->original_sender_email)) {
                    // Use role-based email sending
                    $fromAddress = $unifiedMessageModel->getRoleBasedFromAddress($userRole);

                    require_once APPROOT . '/models/EmailService.php';
                    $emailService = new EmailService();

                    $success = $emailService->send(
                        $originalMessage->original_sender_email,
                        $subject,
                        $message,
                        $message
                    );
                }

                if ($success) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true, 'message' => 'Email reply sent successfully']);
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Reply created but email sending failed']);
                }
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to create reply']);
            }

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * View a specific notification
     */
    public function viewNotification($notificationId) {
        $userId = $this->auth->getCurrentUserId();
        $notificationId = (int)$notificationId;

        // Debug logging
        error_log("NotificationCenterController::viewNotification - User: {$userId}, Notification: {$notificationId}");

        if ($notificationId <= 0) {
            error_log("NotificationCenterController::viewNotification - Invalid notification ID: {$notificationId}");
            $this->setFlashMessage('error', 'Invalid notification ID', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Get message and its thread
        $message = $this->notificationCenterModel->getMessageById($notificationId, $userId);

        if (!$message) {
            error_log("NotificationCenterController::viewNotification - Message not found: ID {$notificationId} for user {$userId}");
            $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Get the full conversation thread
        $messageThread = $this->notificationCenterModel->getMessageThread($notificationId, $userId);
        
        error_log("NotificationCenterController::viewNotification - Successfully loaded message: " . $message->subject);
        error_log("NotificationCenterController::viewNotification - Thread contains " . count($messageThread) . " messages");
        
        // Mark as read
        $this->notificationCenterModel->markAsRead($notificationId, $userId);
        
        $data = [
            'title' => 'View Message',
            'message' => $message,
            'messageThread' => $messageThread,
            'currentUserId' => $userId
        ];
        
        parent::view('notification_center/view', $data);
    }
    
    /**
     * View a specific message (unified system)
     */
    public function viewMessage($messageId) {
        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)$messageId;

        // Debug logging
        error_log("NotificationCenterController::viewMessage - User: {$userId}, Message ID: {$messageId}");

        if ($messageId <= 0) {
            $this->setFlashMessage('error', 'Invalid message ID', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Get message using unified system
        $message = $this->notificationCenterModel->getMessageById($messageId, $userId);

        if (!$message) {
            error_log("NotificationCenterController::viewMessage - Message not found: ID {$messageId} for user {$userId}");
            $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Debug the retrieved message
        error_log("NotificationCenterController::viewMessage - Retrieved message: ID={$message->id}, Subject='{$message->subject}'");
        
        // Mark as read
        $this->notificationCenterModel->markAsRead($messageId, $userId);
        
        // Get the full conversation thread
        $messageThread = $this->notificationCenterModel->getMessageThread($messageId, $userId);
        
        // Check if user can send notifications
        $canSendNotifications = $this->notificationCenterModel->canUserSendNotifications($userId);

        // Get user role for email access
        $userRole = $this->auth->getCurrentUserRole();

        $data = [
            'title' => 'View Message',
            'message' => $message,
            'originalMessage' => $message, // Add a backup copy with a different name
            'messageThread' => $messageThread,
            'currentUserId' => $userId,
            'can_send_notifications' => $canSendNotifications,
            'user_role' => $userRole,
            'has_email_access' => in_array($userRole, ['admin', 'coordinator'])
        ];
        
        parent::view('notification_center/view', $data);
    }
    
    /**
     * Send a reply to a message
     */
    public function reply() {
        error_log("REPLY-DEBUG: Reply method called");
        error_log("REPLY-DEBUG: REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
        error_log("REPLY-DEBUG: POST data: " . print_r($_POST, true));

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            error_log("REPLY-DEBUG: Not POST request, redirecting");
            $this->redirect('notification_center');
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        error_log("REPLY-DEBUG: User ID: " . $userId);

        // Check if user can send notifications
        $canSend = $this->notificationCenterModel->canUserSendNotifications($userId);
        error_log("REPLY-DEBUG: Can user send notifications: " . ($canSend ? 'YES' : 'NO'));

        if (!$canSend) {
            error_log("REPLY-DEBUG: User cannot send notifications");
            $this->setFlashMessage('error', 'You cannot send messages because all your notification types are disabled. Please enable at least one notification type in your settings to send messages.', 'warning');
            $this->redirect('notification_center');
            return;
        }

        $parentMessageId = (int)($_POST['parent_message_id'] ?? 0);
        $message = trim($_POST['message'] ?? '');
        $ticketNumber = trim($_POST['ticket_number'] ?? '');
        $securityToken = trim($_POST['security_token'] ?? '');

        error_log("REPLY-DEBUG: Parent message ID: " . $parentMessageId);
        error_log("REPLY-DEBUG: Message content: " . $message);
        error_log("REPLY-DEBUG: Ticket number: " . $ticketNumber);
        error_log("REPLY-DEBUG: Security token: " . $securityToken);

        if ($parentMessageId <= 0 || empty($message)) {
            error_log("REPLY-DEBUG: Invalid parent message ID or empty message");
            $this->setFlashMessage('error', 'Please provide a valid message', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Check if user can reply to this specific message
        $canReply = $this->notificationCenterModel->canUserReplyToMessage($userId, $parentMessageId);
        error_log("REPLY-DEBUG: Can user reply to message: " . ($canReply ? 'YES' : 'NO'));

        if (!$canReply) {
            error_log("REPLY-DEBUG: User cannot reply to this message");
            $this->setFlashMessage('error', 'You cannot reply to this message. Either it does not allow replies, you have already replied, or you do not have permission.', 'warning');
            $this->redirect('notification_center/viewMessage/' . $parentMessageId);
            return;
        }
        
        try {
            error_log("REPLY-DEBUG: Getting original message by ID: " . $parentMessageId);

            // Get the original message
            $originalMessage = $this->notificationCenterModel->getMessageById($parentMessageId);

            error_log("REPLY-DEBUG: Original message retrieved: " . ($originalMessage ? 'YES' : 'NO'));
            if ($originalMessage) {
                error_log("REPLY-DEBUG: Original message to_user_id: " . $originalMessage->to_user_id . ", current user: " . $userId);
            }

            if (!$originalMessage || $originalMessage->to_user_id != $userId) {
                error_log("REPLY-DEBUG: Message not found or access denied");
                $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
                $this->redirect('notification_center');
                return;
            }
            
            // Check if this is a contact form message
            $isContactForm = $this->isContactFormMessage($originalMessage->message);

            if ($isContactForm) {
                // Extract email from contact form message
                $contactEmail = $this->extractContactEmail($originalMessage->message);

                if ($contactEmail) {
                    // Send email-only reply for contact form messages
                    $emailSent = $this->sendContactFormReply($contactEmail, $originalMessage->subject, $message, $userId);

                    if ($emailSent) {
                        // Also add reply to the message thread for record keeping
                        $replyId = $this->notificationCenterModel->sendReply(
                            $userId,
                            $originalMessage->from_user_id,
                            $originalMessage->subject,
                            $message . "\n\n[This reply was sent via email to: " . $contactEmail . "]",
                            $parentMessageId,
                            $originalMessage->show_id
                        );

                        $this->setFlashMessage('success', 'Reply sent via email to ' . $contactEmail, 'success');
                    } else {
                        $this->setFlashMessage('error', 'Failed to send email reply', 'danger');
                    }
                } else {
                    $this->setFlashMessage('error', 'Could not extract contact email from message', 'danger');
                }
            } else {
                // Check if this is an email message that needs email reply
                $isEmailMessage = ($originalMessage->message_type === 'email');

                // Normal unified messaging reply
                error_log("REPLY-DEBUG: Sending reply via notificationCenterModel->sendReply");
                error_log("REPLY-DEBUG: From user: " . $userId . ", To user: " . $originalMessage->from_user_id);

                $replyId = $this->notificationCenterModel->sendReply(
                    $userId,
                    $originalMessage->from_user_id,
                    $originalMessage->subject,
                    $message,
                    $parentMessageId,
                    $originalMessage->show_id
                );

                error_log("REPLY-DEBUG: sendReply returned ID: " . ($replyId ?: 'FALSE'));

                if ($replyId) {
                    // If this is an email message, also send actual email
                    if ($isEmailMessage && !empty($originalMessage->original_sender_email)) {
                        $emailSent = $this->sendEmailReplyToOriginalSender(
                            $originalMessage->original_sender_email,
                            $originalMessage->subject,
                            $message,
                            $userId
                        );

                        if ($emailSent) {
                            $this->setFlashMessage('success', 'Reply sent successfully (email sent to original sender)', 'success');
                        } else {
                            $this->setFlashMessage('success', 'Reply sent successfully (but email sending failed)', 'warning');
                        }
                    } else {
                        $this->setFlashMessage('success', 'Reply sent successfully', 'success');
                    }
                } else {
                    $this->setFlashMessage('error', 'Failed to send reply', 'danger');
                }
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::reply - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to send reply', 'danger');
        }

        // Redirect back to the message view page instead of notification center
        $this->redirect('notification_center/viewMessage/' . $parentMessageId);
    }
    
    /**
     * Mark notification as read (AJAX)
     */
    public function markRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);
        
        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }
        
        try {
            $success = $this->notificationCenterModel->markAsRead($messageId, $userId);
            
            if ($success) {
                // Get updated counts
                $counts = $this->notificationCenterModel->getMessageCounts($userId);
                $this->jsonResponse([
                    'success' => true,
                    'counts' => $counts
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark as read']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::markRead - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
    
    /**
     * Mark all notifications as read (AJAX)
     */
    public function markAllRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $type = $_POST['type'] ?? 'all';
        
        try {
            $success = $this->notificationCenterModel->markAllAsRead($userId, $type);
            
            if ($success) {
                // Get updated counts from database
                $counts = $this->notificationCenterModel->getMessageCounts($userId);
                
                $this->jsonResponse([
                    'success' => true,
                    'counts' => $counts
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark all as read']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::markAllRead - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
    
    /**
     * Get unread notification count for header badge (AJAX)
     */
    public function getUnreadCount() {
        $userId = $this->auth->getCurrentUserId();
        
        try {
            // Use the reliable database counts
            $counts = $this->notificationCenterModel->getMessageCounts($userId);
            $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);
            
            $this->jsonResponse([
                'success' => true,
                'total_unread' => $counts['total_unread'],
                'counts' => $counts,
                'conversationCounts' => $conversationCounts
            ]);
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::getUnreadCount - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'total_unread' => 0]);
        }
    }
    
    /**
     * Get thread info for confirmation dialogs
     */
    public function getThreadInfo() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $threadInfo = $this->notificationCenterModel->getThreadInfo($messageId, $userId);
            
            if ($threadInfo) {
                $this->jsonResponse([
                    'success' => true, 
                    'total_messages' => $threadInfo->total_messages,
                    'self_replies' => $threadInfo->self_replies
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Thread not found']);
            }
        } catch (Exception $e) {
            error_log("NotificationCenterController::getThreadInfo - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Archive a notification
     */
    public function archive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $success = $this->notificationCenterModel->archiveThread($messageId, $userId);

            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Thread archived successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to archive thread']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::archive - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Unarchive a notification
     */
    public function unarchive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $success = $this->notificationCenterModel->unarchiveThread($messageId, $userId);

            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Thread restored successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to restore thread']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::unarchive - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Delete a notification permanently
     */
    public function delete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $success = $this->notificationCenterModel->deleteThread($messageId, $userId);

            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Thread deleted successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to delete thread']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::delete - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Check if a message is from the contact form
     */
    private function isContactFormMessage($messageContent) {
        // Look for the special marker we added in contact form messages
        return strpos($messageContent, 'CONTACT_EMAIL:') !== false;
    }

    /**
     * Extract contact email from contact form message
     */
    private function extractContactEmail($messageContent) {
        // Extract email from the special marker
        if (preg_match('/CONTACT_EMAIL:([^\s\n]+)/', $messageContent, $matches)) {
            return trim($matches[1]);
        }

        // Fallback: try to extract from "Reply directly to:" line
        if (preg_match('/Reply directly to:\s*([^\s\n]+)/', $messageContent, $matches)) {
            return trim($matches[1]);
        }

        return null;
    }

    /**
     * Send email reply to original sender for email messages
     */
    private function sendEmailReplyToOriginalSender($originalSenderEmail, $originalSubject, $replyMessage, $fromUserId) {
        try {
            // Get sender information
            $senderUser = $this->userModel->getUserById($fromUserId);
            if (!$senderUser) {
                error_log("NotificationCenterController::sendEmailReplyToOriginalSender - Sender user not found: {$fromUserId}");
                return false;
            }

            // Get user role for proper from address
            $userRole = $senderUser->role ?? 'user';

            // Use UnifiedMessageModel to get role-based from address
            require_once APPROOT . '/models/UnifiedMessageModel.php';
            $unifiedMessageModel = new UnifiedMessageModel();
            $fromAddress = $unifiedMessageModel->getRoleBasedFromAddress($userRole);

            // Prepare subject with ticket number if not already present
            $subject = $originalSubject;
            if (strpos($subject, 'Re:') !== 0) {
                $subject = 'Re: ' . $subject;
            }

            // Create email content
            $emailBody = $replyMessage;

            // Send email using EmailService
            require_once APPROOT . '/models/EmailService.php';
            $emailService = new EmailService();

            $success = $emailService->send(
                $originalSenderEmail,
                $subject,
                $emailBody,
                $emailBody
            );

            if ($success) {
                error_log("NotificationCenterController::sendEmailReplyToOriginalSender - Email sent successfully to: {$originalSenderEmail}");
                return true;
            } else {
                error_log("NotificationCenterController::sendEmailReplyToOriginalSender - Failed to send email to: {$originalSenderEmail}");
                return false;
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::sendEmailReplyToOriginalSender - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email reply for contact form messages using the configured EmailService
     */
    private function sendContactFormReply($contactEmail, $originalSubject, $replyMessage, $fromUserId) {
        try {
            // Get sender information
            $senderUser = $this->userModel->getUserById($fromUserId);
            if (!$senderUser) {
                error_log("NotificationCenterController::sendContactFormReply - Sender user not found: {$fromUserId}");
                return false;
            }

            // Load the EmailService
            $emailService = new EmailService();

            // Check if email is configured
            if (!$emailService->isConfigured()) {
                error_log("NotificationCenterController::sendContactFormReply - Email service not configured");
                return false;
            }

            // Prepare email content
            $subject = 'Re: ' . str_replace('Contact Form: ', '', $originalSubject);

            // Create HTML email body
            $htmlBody = "<html><body>";
            $htmlBody .= "<p>Hello,</p>";
            $htmlBody .= "<p>Thank you for contacting us. Here is our response:</p>";
            $htmlBody .= "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>";
            $htmlBody .= nl2br(htmlspecialchars($replyMessage));
            $htmlBody .= "</div>";
            $htmlBody .= "<hr>";
            $htmlBody .= "<p><strong>Best regards,</strong><br>";
            $htmlBody .= htmlspecialchars($senderUser->name) . "<br>";
            $htmlBody .= "Events and Shows Platform</p>";
            $htmlBody .= "<p><small style='color: #666;'>This is a response to your contact form submission.</small></p>";
            $htmlBody .= "</body></html>";

            // Create plain text version
            $plainBody = "Hello,\n\n";
            $plainBody .= "Thank you for contacting us. Here is our response:\n\n";
            $plainBody .= $replyMessage . "\n\n";
            $plainBody .= "---\n";
            $plainBody .= "Best regards,\n";
            $plainBody .= $senderUser->name . "\n";
            $plainBody .= "Events and Shows Platform\n\n";
            $plainBody .= "This is a response to your contact form submission.";

            // Send email using the configured EmailService
            $emailSent = $emailService->send($contactEmail, $subject, $htmlBody, $plainBody);

            if ($emailSent) {
                error_log("NotificationCenterController::sendContactFormReply - Email sent successfully to: {$contactEmail} via EmailService");
            } else {
                error_log("NotificationCenterController::sendContactFormReply - Failed to send email to: {$contactEmail} via EmailService");
            }

            return $emailSent;

        } catch (Exception $e) {
            error_log("NotificationCenterController::sendContactFormReply - Error: " . $e->getMessage());
            return false;
        }
    }

    // ==========================================
    // EMAIL FOLDER MANAGEMENT METHODS
    // ==========================================

    /**
     * Get email folders for admin/coordinator
     */
    public function getFolders() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        // Only admin and coordinator can access folders
        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        try {
            $folders = $this->emailFolderModel->getFolderStatistics();

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'folders' => $folders]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error loading folders']);
        }
    }

    /**
     * Move message to folder
     */
    public function moveToFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        // Only admin can move messages to folders
        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageId = (int)($_POST['message_id'] ?? 0);
        $folderId = (int)($_POST['folder_id'] ?? 0);

        if (!$messageId || !$folderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message ID and folder ID are required']);
            return;
        }

        // Prevent moving to Archive folder (ID 6) - use archive action instead
        if ($folderId == 6) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Cannot move to Archive folder. Use Archive action instead.']);
            return;
        }

        try {
            $success = $this->emailFolderModel->moveMessageToFolder($messageId, $folderId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Message moved to folder successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to move message']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Create new folder
     */
    public function createFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        // Only admin can create folders
        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $color = $_POST['color'] ?? '#007bff';
        $icon = $_POST['icon'] ?? 'fas fa-folder';

        if (empty($name)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Folder name is required']);
            return;
        }

        try {
            $folderId = $this->emailFolderModel->createFolder($name, $description, $color, $icon, $userId);

            if ($folderId) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Folder created successfully', 'folder_id' => $folderId]);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to create folder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Create new email template
     */
    public function createTemplate() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        // Only admin can create templates
        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $name = trim($_POST['name'] ?? '');
        $subject = trim($_POST['subject'] ?? '');
        $body = trim($_POST['body'] ?? '');
        $category = $_POST['category'] ?? 'general';

        if (empty($name) || empty($subject) || empty($body)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Name, subject, and body are required']);
            return;
        }

        try {
            $templateId = $this->emailTemplateModel->createTemplate($name, $subject, $body, '', $category, $userId);

            if ($templateId) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Template created successfully', 'template_id' => $templateId]);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to create template']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    // ==========================================
    // EMAIL REMINDER MANAGEMENT METHODS
    // ==========================================

    /**
     * Set email reminder
     */
    public function setReminder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        // Only admin can set reminders
        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageId = (int)($_POST['message_id'] ?? 0);
        $reminderDate = $_POST['reminder_date'] ?? '';
        $reminderText = trim($_POST['reminder_text'] ?? '');

        if (!$messageId || empty($reminderDate)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message ID and reminder date are required']);
            return;
        }

        try {
            $reminderId = $this->emailReminderModel->createReminder($messageId, $userId, $reminderDate, $reminderText);

            if ($reminderId) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Reminder set successfully', 'reminder_id' => $reminderId]);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to set reminder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get user reminders
     */
    public function getReminders() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();

        // Only admin can view reminders
        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        try {
            $reminders = $this->emailReminderModel->getUserReminders($userId);
            $stats = $this->emailReminderModel->getReminderStats($userId);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'reminders' => $reminders,
                'stats' => $stats
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error loading reminders']);
        }
    }

    /**
     * Complete reminder
     */
    public function completeReminder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        // Only admin can complete reminders
        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $reminderId = (int)($_POST['reminder_id'] ?? 0);

        if (!$reminderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Reminder ID is required']);
            return;
        }

        try {
            $success = $this->emailReminderModel->completeReminder($reminderId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Reminder completed successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to complete reminder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    // ==========================================
    // ADDITIONAL EMAIL MANAGEMENT METHODS
    // ==========================================

    /**
     * Update email template
     */
    public function updateTemplate() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $templateId = (int)($_POST['template_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $subject = trim($_POST['subject'] ?? '');
        $body = trim($_POST['body'] ?? '');
        $category = $_POST['category'] ?? 'general';

        if (!$templateId || empty($name) || empty($subject) || empty($body)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'All fields are required']);
            return;
        }

        try {
            $success = $this->emailTemplateModel->updateTemplate($templateId, $name, $subject, $body, '', $category);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Template updated successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to update template']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete email template
     */
    public function deleteTemplate() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $templateId = (int)($_POST['template_id'] ?? 0);

        if (!$templateId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Template ID is required']);
            return;
        }

        try {
            $success = $this->emailTemplateModel->deleteTemplate($templateId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Template deleted successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to delete template']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Update email folder
     */
    public function updateFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $folderId = (int)($_POST['folder_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $color = $_POST['color'] ?? '#007bff';
        $icon = $_POST['icon'] ?? 'fas fa-folder';

        if (!$folderId || empty($name)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Folder ID and name are required']);
            return;
        }

        try {
            $success = $this->emailFolderModel->updateFolder($folderId, $name, $description, $color, $icon);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Folder updated successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to update folder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete email folder
     */
    public function deleteFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $folderId = (int)($_POST['folder_id'] ?? 0);

        if (!$folderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Folder ID is required']);
            return;
        }

        try {
            $success = $this->emailFolderModel->deleteFolder($folderId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Folder deleted successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to delete folder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Update reminder
     */
    public function updateReminder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $reminderId = (int)($_POST['reminder_id'] ?? 0);
        $reminderDate = $_POST['reminder_date'] ?? '';
        $reminderText = trim($_POST['reminder_text'] ?? '');

        if (!$reminderId || empty($reminderDate)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Reminder ID and date are required']);
            return;
        }

        try {
            $success = $this->emailReminderModel->updateReminder($reminderId, $reminderDate, $reminderText);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Reminder updated successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to update reminder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete reminder
     */
    public function deleteReminder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $reminderId = (int)($_POST['reminder_id'] ?? 0);

        if (!$reminderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Reminder ID is required']);
            return;
        }

        try {
            $success = $this->emailReminderModel->deleteReminder($reminderId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Reminder deleted successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to delete reminder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Snooze reminder
     */
    public function snoozeReminder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $reminderId = (int)($_POST['reminder_id'] ?? 0);
        $hours = (int)($_POST['hours'] ?? 1);

        if (!$reminderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Reminder ID is required']);
            return;
        }

        try {
            $success = $this->emailReminderModel->snoozeReminder($reminderId, $hours);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Reminder snoozed successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to snooze reminder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk move messages to folder
     */
    public function bulkMoveToFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageIdsString = $_POST['message_ids'] ?? '';
        $folderId = (int)($_POST['folder_id'] ?? 0);

        if (empty($messageIdsString) || !$folderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message IDs and folder ID are required']);
            return;
        }

        // Prevent moving to Archive folder (ID 6) - use archive action instead
        if ($folderId == 6) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Cannot move to Archive folder. Use Archive action instead.']);
            return;
        }

        // Convert comma-separated string to array of integers
        $messageIds = array_map('intval', explode(',', $messageIdsString));

        try {
            $success = $this->emailFolderModel->bulkMoveMessages($messageIds, $folderId);

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Messages moved successfully']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to move messages']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Export emails
     */
    public function exportEmails() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $folderId = (int)($_GET['folder_id'] ?? 0);
        $format = $_GET['format'] ?? 'csv';

        try {
            if ($folderId) {
                $messages = $this->emailFolderModel->getMessagesInFolder($folderId, 1000, 0);
            } else {
                // Get all email messages
                $this->db->query("SELECT * FROM messages WHERE message_type = 'email' ORDER BY created_at DESC LIMIT 1000");
                $messages = $this->db->resultSet();
            }

            if ($format === 'csv') {
                header('Content-Type: text/csv');
                header('Content-Disposition: attachment; filename="emails_export_' . date('Y-m-d') . '.csv"');

                $output = fopen('php://output', 'w');
                fputcsv($output, ['ID', 'Subject', 'From', 'To', 'Message', 'Created At', 'Folder']);

                foreach ($messages as $message) {
                    fputcsv($output, [
                        $message->id,
                        $message->subject,
                        $message->original_sender_email ?? 'System',
                        $message->to_user_id,
                        substr($message->message, 0, 100),
                        $message->created_at,
                        $message->folder_name ?? 'Inbox'
                    ]);
                }

                fclose($output);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'messages' => $messages]);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get email statistics
     */
    public function getEmailStatistics() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        try {
            // Get folder statistics
            $folderStats = $this->emailFolderModel->getFolderStatistics();

            // Get overall statistics
            $this->db->query("SELECT
                COUNT(*) as total_emails,
                COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_emails,
                COUNT(CASE WHEN folder_id = 3 THEN 1 END) as follow_up_emails,
                COUNT(CASE WHEN folder_id = 4 THEN 1 END) as resolved_emails,
                COUNT(CASE WHEN created_at >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL 7 DAY) THEN 1 END) as this_week_emails,
                COUNT(CASE WHEN created_at >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL 30 DAY) THEN 1 END) as this_month_emails
                FROM messages WHERE message_type = 'email'");
            $overallStats = $this->db->single();

            // Get template usage statistics
            $this->db->query("SELECT COUNT(*) as total_templates FROM email_templates WHERE is_active = 1");
            $templateStats = $this->db->single();

            // Get reminder statistics
            $this->db->query("SELECT
                COUNT(*) as total_reminders,
                COUNT(CASE WHEN is_completed = 0 THEN 1 END) as active_reminders,
                COUNT(CASE WHEN reminder_date <= UTC_TIMESTAMP() AND is_completed = 0 THEN 1 END) as overdue_reminders
                FROM email_reminders");
            $reminderStats = $this->db->single();

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'folder_stats' => $folderStats,
                'overall_stats' => $overallStats,
                'template_stats' => $templateStats,
                'reminder_stats' => $reminderStats
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk mark as read
     */
    public function bulkMarkAsRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageIds = explode(',', $_POST['message_ids'] ?? '');
        $messageIds = array_filter(array_map('intval', $messageIds));

        if (empty($messageIds)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No message IDs provided']);
            return;
        }

        try {
            $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
            $this->db->query("UPDATE messages SET is_read = 1 WHERE id IN ($placeholders)");
            $bindIndex = 1;
            foreach ($messageIds as $id) {
                $this->db->bind($bindIndex++, $id);
            }
            $this->db->execute();

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => count($messageIds) . ' messages marked as read']);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk archive
     */
    public function bulkArchive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageIds = explode(',', $_POST['message_ids'] ?? '');
        $messageIds = array_filter(array_map('intval', $messageIds));

        if (empty($messageIds)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No message IDs provided']);
            return;
        }

        try {
            // Update messages: set as archived only (don't move to archive folder)
            // Archive folder is handled by main archived tab, not email management
            $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
            $this->db->query("UPDATE messages SET is_archived = 1 WHERE id IN ($placeholders)");
            
            $bindIndex = 1;
            foreach ($messageIds as $id) {
                $this->db->bind($bindIndex++, $id);
            }
            $this->db->execute();

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => count($messageIds) . ' conversations archived']);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk delete
     */
    public function bulkDelete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageIds = explode(',', $_POST['message_ids'] ?? '');
        $messageIds = array_filter(array_map('intval', $messageIds));

        if (empty($messageIds)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No message IDs provided']);
            return;
        }

        try {
            $this->db->beginTransaction();

            // Get ticket numbers that will be deleted (for cleanup)
            $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
            $this->db->query("SELECT DISTINCT ticket_number FROM messages WHERE id IN ($placeholders) AND ticket_number IS NOT NULL");
            $bindIndex = 1;
            foreach ($messageIds as $id) {
                $this->db->bind($bindIndex++, $id);
            }
            $ticketsToCleanup = $this->db->resultSet();

            // Delete the messages
            $this->db->query("DELETE FROM messages WHERE id IN ($placeholders)");
            $bindIndex = 1;
            foreach ($messageIds as $id) {
                $this->db->bind($bindIndex++, $id);
            }
            $this->db->execute();

            // Clean up orphaned tickets
            if (!empty($ticketsToCleanup)) {
                require_once APPROOT . '/models/UnifiedMessageModel.php';
                $unifiedModel = new UnifiedMessageModel();

                foreach ($ticketsToCleanup as $ticket) {
                    // Check if ticket is now orphaned and clean it up
                    $this->db->query("SELECT COUNT(*) as count FROM messages WHERE ticket_number = :ticket_number");
                    $this->db->bind(':ticket_number', $ticket->ticket_number);
                    $result = $this->db->single();

                    if ($result && $result->count == 0) {
                        // Clean up from ticket_numbers table if it exists
                        $this->db->query("DELETE FROM ticket_numbers WHERE ticket_number = :ticket_number");
                        $this->db->bind(':ticket_number', $ticket->ticket_number);
                        $this->db->execute();

                        error_log("NotificationCenterController::bulkDelete - Cleaned up orphaned ticket: " . $ticket->ticket_number);
                    }
                }
            }

            $this->db->commit();

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => count($messageIds) . ' messages deleted']);
        } catch (Exception $e) {
            $this->db->rollback();
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Complete reminder (new implementation)
     */
    public function completeReminderNew() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $reminderId = (int)($_POST['reminder_id'] ?? 0);

        if (!$reminderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Reminder ID is required']);
            return;
        }

        try {
            $this->db->query("UPDATE email_reminders SET is_completed = 1 WHERE id = ?");
            $this->db->bind(1, $reminderId);
            $success = $this->db->execute();

            if ($success) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Reminder completed']);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to complete reminder']);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get emails by show (show-specific organization)
     */
    public function getEmailsByShow() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $showId = (int)($_GET['show_id'] ?? 0);

        try {
            if ($showId) {
                if ($userRole === 'admin') {
                    // Admin can see all emails for any show
                    $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color, s.name as show_name
                                     FROM messages m
                                     LEFT JOIN email_folders f ON m.folder_id = f.id
                                     LEFT JOIN shows s ON m.show_id = s.id
                                     WHERE m.message_type = 'email' AND m.show_id = ?
                                     ORDER BY m.created_at DESC
                                     LIMIT 200");  // Get more messages for proper grouping
                    $this->db->bind(1, $showId);
                } else {
                    // Coordinator can only see emails for shows they coordinate
                    $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color, s.name as show_name
                                     FROM messages m
                                     LEFT JOIN email_folders f ON m.folder_id = f.id
                                     LEFT JOIN shows s ON m.show_id = s.id
                                     INNER JOIN show_role_assignments sr ON s.id = sr.show_id
                                     WHERE m.message_type = 'email' AND m.show_id = ?
                                     AND sr.user_id = ? AND sr.role = 'coordinator'
                                     AND (s.status != 'completed' OR
                                          (s.status = 'completed' AND s.end_date >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL 7 DAY)))
                                     ORDER BY m.created_at DESC
                                     LIMIT 200");  // Get more messages for proper grouping
                    $this->db->bind(1, $showId);
                    $this->db->bind(2, $userId);
                }
            } else {
                if ($userRole === 'admin') {
                    // Admin can see all emails
                    $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color, s.name as show_name
                                     FROM messages m
                                     LEFT JOIN email_folders f ON m.folder_id = f.id
                                     LEFT JOIN shows s ON m.show_id = s.id
                                     WHERE m.message_type = 'email'
                                     ORDER BY m.created_at DESC
                                     LIMIT 200");  // Get more messages for proper grouping
                } else {
                    // Coordinator can only see emails for shows they coordinate
                    $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color, s.name as show_name
                                     FROM messages m
                                     LEFT JOIN email_folders f ON m.folder_id = f.id
                                     LEFT JOIN shows s ON m.show_id = s.id
                                     INNER JOIN show_role_assignments sr ON s.id = sr.show_id
                                     WHERE m.message_type = 'email'
                                     AND sr.user_id = ? AND sr.role = 'coordinator'
                                     ORDER BY m.created_at DESC
                                     LIMIT 200");  // Get more messages for proper grouping
                    $this->db->bind(1, $userId);
                }
            }

            $emailsRaw = $this->db->resultSet();

            // Group emails by ticket number for threaded display (use actual counts for email management)
            $emails = $this->groupMessagesByTicketNumber($emailsRaw, true);

            // Limit to 50 conversations after grouping
            $emails = array_slice($emails, 0, 50);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'emails' => $emails,
                'count' => count($emails)
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Search shows for admin (handles thousands of shows efficiently)
     */
    public function searchShows() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $searchTerm = trim($_GET['q'] ?? '');

        if (strlen($searchTerm) < 2) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Search term must be at least 2 characters']);
            return;
        }

        try {
            // Search shows with LIMIT to prevent performance issues
            $this->db->query("SELECT id, name, status, start_date, end_date
                             FROM shows
                             WHERE name LIKE ?
                             ORDER BY
                                CASE WHEN status = 'active' THEN 1
                                     WHEN status = 'upcoming' THEN 2
                                     WHEN status = 'completed' THEN 3
                                     ELSE 4 END,
                                start_date DESC
                             LIMIT 20");
            $this->db->bind(1, '%' . $searchTerm . '%');
            $shows = $this->db->resultSet();

            // Format dates for display
            foreach ($shows as $show) {
                $show->start_date = date('M j, Y', strtotime($show->start_date));
                $show->end_date = date('M j, Y', strtotime($show->end_date));
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'shows' => $shows,
                'count' => count($shows)
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get emails by folder
     */
    public function getEmailsByFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $folderId = (int)($_GET['folder_id'] ?? 0);

        if (!$folderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Folder ID is required']);
            return;
        }

        try {
            if ($userRole === 'admin') {
                // Admin can see all emails in any folder
                $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color, s.name as show_name
                                 FROM messages m
                                 LEFT JOIN email_folders f ON m.folder_id = f.id
                                 LEFT JOIN shows s ON m.show_id = s.id
                                 WHERE m.message_type = 'email' AND m.folder_id = ? AND m.is_archived = 0
                                 ORDER BY m.created_at DESC
                                 LIMIT 200");  // Get more messages for proper grouping
                $this->db->bind(1, $folderId);
            } else {
                // Coordinator can only see emails for shows they coordinate
                $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color, s.name as show_name
                                 FROM messages m
                                 LEFT JOIN email_folders f ON m.folder_id = f.id
                                 LEFT JOIN shows s ON m.show_id = s.id
                                 INNER JOIN show_role_assignments sr ON s.id = sr.show_id
                                 WHERE m.message_type = 'email' AND m.folder_id = ? AND m.is_archived = 0
                                 AND sr.user_id = ? AND sr.role = 'coordinator'
                                 AND (s.status != 'completed' OR
                                      (s.status = 'completed' AND s.end_date >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL 7 DAY)))
                                 ORDER BY m.created_at DESC
                                 LIMIT 200");  // Get more messages for proper grouping
                $this->db->bind(1, $folderId);
                $this->db->bind(2, $userId);
            }

            $emailsRaw = $this->db->resultSet();
            
            // Group emails by ticket number (use actual counts for email management)
            $emails = $this->groupMessagesByTicketNumber($emailsRaw, true);
            
            // Limit to 50 conversations after grouping
            $emails = array_slice($emails, 0, 50);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'emails' => $emails,
                'count' => count($emails)
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get all message IDs in a conversation (for bulk actions)
     */
    public function getConversationMessageIds() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageId = (int)($_GET['message_id'] ?? 0);

        if (!$messageId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message ID is required']);
            return;
        }

        try {
            // Get the message to find its ticket number (for email messages only)
            $this->db->query("SELECT ticket_number, parent_message_id FROM messages WHERE id = ? AND message_type = 'email'");
            $this->db->bind(1, $messageId);
            $message = $this->db->single();

            if (!$message) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Message not found or not an email']);
                return;
            }

            $messageIds = [];

            if ($message->ticket_number) {
                // Get ALL messages with the same ticket number (entire conversation, all message types)
                $this->db->query("SELECT id FROM messages WHERE ticket_number = ? ORDER BY created_at ASC");
                $this->db->bind(1, $message->ticket_number);
                $conversationMessages = $this->db->resultSet();
                
                $messageIds = array_map(function($msg) { return (int)$msg->id; }, $conversationMessages);
            } else {
                // No ticket number, just return the single message ID
                $messageIds = [$messageId];
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message_ids' => $messageIds,
                'count' => count($messageIds)
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get recent shows for admin
     */
    public function getRecentShows() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        try {
            // Get recent shows (active and recently completed)
            $this->db->query("SELECT id, name, status, start_date, end_date
                             FROM shows
                             WHERE status = 'active' OR
                                   (status = 'completed' AND end_date >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL 30 DAY))
                             ORDER BY
                                CASE WHEN status = 'active' THEN 1
                                     WHEN status = 'upcoming' THEN 2
                                     WHEN status = 'completed' THEN 3
                                     ELSE 4 END,
                                start_date DESC
                             LIMIT 10");
            $shows = $this->db->resultSet();

            // Format dates for display
            foreach ($shows as $show) {
                $show->start_date = date('M j, Y', strtotime($show->start_date));
                $show->end_date = date('M j, Y', strtotime($show->end_date));
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'shows' => $shows,
                'count' => count($shows)
            ]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Force email check (Admin only) - Manually trigger the email cron job
     */
    public function forceEmailCheck() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();

        if ($userRole !== 'admin') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied - Admin only']);
            return;
        }

        try {
            // Get the absolute path to the cron script
            $cronScript = APPROOT . '/cron/process_incoming_emails.php';

            if (!file_exists($cronScript)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Email processing script not found']);
                return;
            }

            // Log the manual trigger
            error_log("NotificationCenter::forceEmailCheck - Manual email check triggered by admin user ID: " . $this->auth->getCurrentUserId());

            // Execute the cron script
            $startTime = microtime(true);

            // Use exec to run the PHP script and capture output
            $command = "php " . escapeshellarg($cronScript) . " 2>&1";
            $output = [];
            $returnCode = 0;

            exec($command, $output, $returnCode);

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            // Log the results
            $outputString = implode("\n", $output);
            error_log("NotificationCenter::forceEmailCheck - Execution completed in {$executionTime}s, return code: $returnCode");
            error_log("NotificationCenter::forceEmailCheck - Output: " . $outputString);

            if ($returnCode === 0) {
                // Success - check if any emails were processed
                $emailsProcessed = 0;

                // Try to extract number of emails from output
                if (preg_match('/(\d+)\s+emails?\s+processed/i', $outputString, $matches)) {
                    $emailsProcessed = (int)$matches[1];
                } elseif (preg_match('/Fetched\s+(\d+)\s+emails?\s+for\s+processing/i', $outputString, $matches)) {
                    $emailsProcessed = (int)$matches[1];
                }

                $message = "Email check completed successfully in {$executionTime}s";
                if ($emailsProcessed > 0) {
                    $message .= " - {$emailsProcessed} emails processed";
                } else {
                    $message .= " - No new emails found";
                }

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'execution_time' => $executionTime,
                    'emails_processed' => $emailsProcessed,
                    'output' => $outputString
                ]);
            } else {
                // Error occurred
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => "Email check failed (exit code: $returnCode)",
                    'execution_time' => $executionTime,
                    'output' => $outputString,
                    'error_code' => $returnCode
                ]);
            }

        } catch (Exception $e) {
            error_log("NotificationCenter::forceEmailCheck - Exception: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Error executing email check: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Assign a show to an email message/thread
     * Handles both admin (all shows) and coordinator (their shows only) access
     */
    public function assignShowToMessage() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        // Only admin and coordinators can assign shows
        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $messageId = (int)($_POST['message_id'] ?? 0);
        $showId = (int)($_POST['show_id'] ?? 0);

        if (!$messageId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message ID is required']);
            return;
        }

        try {
            // Get the original message to verify access and get ticket number
            $this->db->query("SELECT * FROM messages WHERE id = :message_id AND message_type = 'email'");
            $this->db->bind(':message_id', $messageId);
            $message = $this->db->single();

            if (!$message) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Email message not found']);
                return;
            }

            // For coordinators, verify they have access to assign this show
            if ($userRole === 'coordinator' && $showId > 0) {
                $this->db->query("SELECT COUNT(*) as count FROM show_role_assignments
                                 WHERE user_id = :user_id AND show_id = :show_id
                                 AND assigned_role = 'coordinator' AND is_active = 1");
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':show_id', $showId);
                $result = $this->db->single();

                if ($result->count == 0) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'You can only assign shows you coordinate']);
                    return;
                }

                // Also verify the show is not completed
                $this->db->query("SELECT status FROM shows WHERE id = :show_id");
                $this->db->bind(':show_id', $showId);
                $show = $this->db->single();

                if ($show && $show->status === 'completed') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Cannot assign completed shows']);
                    return;
                }
            }

            // Update all messages in the thread (same ticket number) with the show assignment
            if ($message->ticket_number) {
                $this->db->query("UPDATE messages SET show_id = :show_id
                                 WHERE ticket_number = :ticket_number");
                $this->db->bind(':show_id', $showId > 0 ? $showId : null);
                $this->db->bind(':ticket_number', $message->ticket_number);
            } else {
                // No ticket number, just update this message
                $this->db->query("UPDATE messages SET show_id = :show_id WHERE id = :message_id");
                $this->db->bind(':show_id', $showId > 0 ? $showId : null);
                $this->db->bind(':message_id', $messageId);
            }

            $success = $this->db->execute();

            if ($success) {
                // Get show details for response
                $showDetails = null;
                if ($showId > 0) {
                    $this->db->query("SELECT name, location FROM shows WHERE id = :show_id");
                    $this->db->bind(':show_id', $showId);
                    $showDetails = $this->db->single();
                }

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => $showId > 0 ? 'Show assigned successfully' : 'Show assignment removed',
                    'show_details' => $showDetails
                ]);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Failed to update show assignment']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenter::assignShowToMessage - Error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Search shows for assignment (coordinator-specific filtering)
     * Coordinators only see their active shows, admins see all shows
     */
    public function searchShowsForAssignment() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        if (!in_array($userRole, ['admin', 'coordinator'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $searchTerm = trim($_GET['q'] ?? '');

        if (strlen($searchTerm) < 2) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Search term must be at least 2 characters']);
            return;
        }

        try {
            if ($userRole === 'admin') {
                // Admins can search all shows
                $this->db->query("SELECT id, name, status, start_date, end_date, location
                                 FROM shows
                                 WHERE name LIKE ?
                                 ORDER BY
                                    CASE WHEN status = 'active' THEN 1
                                         WHEN status = 'upcoming' THEN 2
                                         WHEN status = 'completed' THEN 3
                                         ELSE 4 END,
                                    start_date DESC
                                 LIMIT 20");
                $this->db->bind(1, '%' . $searchTerm . '%');
            } else {
                // Coordinators only see their active shows (not completed)
                $this->db->query("SELECT s.id, s.name, s.status, s.start_date, s.end_date, s.location
                                 FROM shows s
                                 INNER JOIN show_role_assignments sra ON s.id = sra.show_id
                                 WHERE s.name LIKE ?
                                 AND sra.user_id = ?
                                 AND sra.assigned_role = 'coordinator'
                                 AND sra.is_active = 1
                                 AND s.status != 'completed'
                                 ORDER BY s.start_date DESC
                                 LIMIT 20");
                $this->db->bind(1, '%' . $searchTerm . '%');
                $this->db->bind(2, $userId);
            }

            $shows = $this->db->resultSet();

            // Format dates for display
            foreach ($shows as $show) {
                $show->start_date = date('M j, Y', strtotime($show->start_date));
                $show->end_date = date('M j, Y', strtotime($show->end_date));
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'shows' => $shows,
                'count' => count($shows)
            ]);

        } catch (Exception $e) {
            error_log("NotificationCenter::searchShowsForAssignment - Error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get coordinator shows for dropdown (active shows only)
     */
    public function getCoordinatorShows() {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userRole = $this->auth->getCurrentUserRole();
        $userId = $this->auth->getCurrentUserId();

        if ($userRole !== 'coordinator') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Access denied - coordinators only']);
            return;
        }

        try {
            // Get coordinator's active shows (not completed)
            $this->db->query("SELECT s.id, s.name, s.status, s.start_date, s.end_date, s.location
                             FROM shows s
                             INNER JOIN show_role_assignments sra ON s.id = sra.show_id
                             WHERE sra.user_id = ?
                             AND sra.assigned_role = 'coordinator'
                             AND sra.is_active = 1
                             AND s.status != 'completed'
                             ORDER BY s.start_date DESC");
            $this->db->bind(1, $userId);
            $shows = $this->db->resultSet();

            // Format dates for display
            foreach ($shows as $show) {
                $show->start_date = date('M j, Y', strtotime($show->start_date));
                $show->end_date = date('M j, Y', strtotime($show->end_date));
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'shows' => $shows,
                'count' => count($shows)
            ]);

        } catch (Exception $e) {
            error_log("NotificationCenter::getCoordinatorShows - Error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }
}
