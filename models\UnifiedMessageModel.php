<?php
/**
 * Unified Message Model
 * 
 * This model handles all messaging functionality in a clean, unified way.
 * Replaces the complex notification system with a simple, effective solution.
 */
class UnifiedMessageModel {
    private $db;
    private $notificationModel;
    private $ticketService;

    public function __construct() {
        $this->db = new Database();
        // Initialize NotificationModel to access global settings
        require_once APPROOT . '/models/NotificationModel.php';
        $this->notificationModel = new NotificationModel();

        // Initialize Enhanced Ticket Service for email system
        require_once APPROOT . '/models/EnhancedTicketService.php';

        // Load ContentCleaner for message content cleanup
        require_once APPROOT . '/core/ContentCleaner.php';
        $this->ticketService = new EnhancedTicketService();
    }
    
    /**
     * Send a message to a user
     * This handles all delivery methods based on user preferences
     */
    public function sendMessage($fromUserId, $toUserId, $subject, $message, $showId = null, $messageType = 'direct', $requiresReply = false, $parentMessageId = null) {
        try {
            // Set default folder for email messages (Inbox = folder_id 1)
            $folderId = null;
            if ($messageType === 'email') {
                $folderId = 1; // Default to Inbox folder for email messages
                error_log("UnifiedMessageModel::sendMessage - Setting folder_id = 1 for email message: $subject");
            }

            // Handle threading - inherit ticket_number and security_token from parent message
            $ticketNumber = null;
            $securityToken = null;
            
            if ($parentMessageId) {
                error_log("UnifiedMessageModel::sendMessage - Getting parent message threading info for ID: $parentMessageId");
                $parentSql = "SELECT ticket_number, security_token FROM messages WHERE id = :parent_id";
                $this->db->query($parentSql);
                $this->db->bind(':parent_id', $parentMessageId);
                $parentResult = $this->db->single();
                
                if ($parentResult) {
                    $ticketNumber = $parentResult->ticket_number;
                    $securityToken = $parentResult->security_token;
                    error_log("UnifiedMessageModel::sendMessage - Inherited threading: ticket_number=$ticketNumber, security_token=$securityToken");
                } else {
                    error_log("UnifiedMessageModel::sendMessage - Warning: Parent message not found for ID: $parentMessageId");
                }
            }

            // Insert message into unified table (no transaction for simplicity)
            $sql = "INSERT INTO messages
                    (from_user_id, to_user_id, subject, message, show_id, message_type, requires_reply, parent_message_id, folder_id, ticket_number, security_token, created_at)
                    VALUES (:from_user_id, :to_user_id, :subject, :message, :show_id, :message_type, :requires_reply, :parent_message_id, :folder_id, :ticket_number, :security_token, NOW())";

            error_log("UnifiedMessageModel::sendMessage - SQL: $sql");
            error_log("UnifiedMessageModel::sendMessage - Parameters: fromUserId=$fromUserId, toUserId=$toUserId, messageType=$messageType, folderId=$folderId, ticketNumber=$ticketNumber, securityToken=$securityToken");

            $this->db->query($sql);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->bind(':to_user_id', $toUserId);
            $this->db->bind(':subject', $subject);
            $this->db->bind(':message', $message);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':message_type', $messageType);
            $this->db->bind(':requires_reply', $requiresReply ? 1 : 0);
            $this->db->bind(':parent_message_id', $parentMessageId);
            $this->db->bind(':folder_id', $folderId);
            $this->db->bind(':ticket_number', $ticketNumber);
            $this->db->bind(':security_token', $securityToken);

            if (!$this->db->execute()) {
                error_log("UnifiedMessageModel::sendMessage - Database execute failed");
                throw new Exception("Failed to insert message");
            }

            $messageId = $this->db->lastInsertId();
            error_log("UnifiedMessageModel::sendMessage - Message created successfully with ID: $messageId");

            // Send via user's preferred delivery methods
            $this->deliverMessage($messageId, $toUserId);

            return $messageId;

        } catch (Exception $e) {
            error_log("UnifiedMessageModel::sendMessage - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Deliver message via user's preferred methods
     * Now properly checks both global settings and user preferences
     */
    private function deliverMessage($messageId, $userId) {
        // Get global notification settings first
        $globalSettings = $this->getGlobalNotificationSettings();

        // Get user's notification preferences
        $preferences = $this->getUserNotificationPreferences($userId);

        if (!$preferences) {
            // Create default preferences if none exist
            $this->createDefaultNotificationPreferences($userId);
            $preferences = $this->getUserNotificationPreferences($userId);
        }

        // Get message details
        $message = $this->getMessageById($messageId);
        if (!$message) return;

        // Get sender name
        $sender = $this->getUserById($message->from_user_id);
        $senderName = $sender ? $sender->name : 'System';

        // Deliver via enabled methods (check BOTH global settings AND user preferences)
        if ($globalSettings['email_enabled'] && $preferences->email_enabled) {
            $this->deliverViaEmail($messageId, $message, $senderName);
        }

        if ($globalSettings['push_enabled'] && $preferences->push_enabled) {
            $this->deliverViaPush($messageId, $message, $senderName);
        }

        if ($globalSettings['toast_enabled'] && $preferences->toast_enabled) {
            $this->deliverViaToast($messageId, $message, $senderName);
        }

        if ($globalSettings['sms_enabled'] && $preferences->sms_enabled) {
            $this->deliverViaSMS($messageId, $message, $senderName);
        }
    }
    
    /**
     * Deliver message via email
     */
    private function deliverViaEmail($messageId, $message, $senderName) {
        try {
            // Get recipient email
            $recipient = $this->getUserById($message->to_user_id);
            if (!$recipient || !$recipient->email) {
                $this->trackDelivery($messageId, 'email', 'failed', 'No email address');
                return;
            }

            // Prepare email content with descriptive subject
            $emailSubject = !empty($message->subject) ?
                "New Message: {$message->subject}" :
                "New Message from {$senderName}";

            // Create the notification center link
            $notificationCenterUrl = defined('BASE_URL') ? BASE_URL . '/notification_center' : 'https://' . $_SERVER['HTTP_HOST'] . '/notification_center';

            // Clean the message content for email
            $cleanSubject = ContentCleaner::cleanForEmail($message->subject);
            $cleanMessage = ContentCleaner::cleanForEmail($message->message);

            // Create plain text version
            $plainTextBody = "Hello {$recipient->name},\n\n";
            $plainTextBody .= "You have received a message from {$senderName}:\n\n";
            $plainTextBody .= "Subject: " . strip_tags($cleanSubject) . "\n\n";
            $plainTextBody .= strip_tags($cleanMessage) . "\n\n";
            $plainTextBody .= "You can view and reply to this message in your notification center:\n";
            $plainTextBody .= $notificationCenterUrl . "\n\n";
            $plainTextBody .= "Thank you!";

            // Create HTML version for better presentation
            $htmlBody = "<html><body>";
            $htmlBody .= "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>";
            $htmlBody .= "<h2 style='color: #333;'>" . htmlspecialchars($emailSubject) . "</h2>";
            $htmlBody .= "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
            $htmlBody .= "<p><strong>Hello " . htmlspecialchars($recipient->name) . ",</strong></p>";
            $htmlBody .= "<p>You have received a message from <strong>" . htmlspecialchars($senderName) . "</strong>:</p>";
            $htmlBody .= "<h3 style='color: #555;'>Subject: " . htmlspecialchars($message->subject) . "</h3>";

            // Convert URLs to clickable links and preserve line breaks
            $messageWithLinks = $this->convertUrlsToLinks($message->message);
            $htmlBody .= "<div style='background-color: #fff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
            $htmlBody .= nl2br($messageWithLinks);
            $htmlBody .= "</div>";

            $htmlBody .= "<p>You can view and reply to this message in your notification center:</p>";
            $htmlBody .= "<p><a href='" . htmlspecialchars($notificationCenterUrl) . "' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>View Message</a></p>";
            $htmlBody .= "</div>";
            $htmlBody .= "<hr style='border: 1px solid #ddd; margin: 20px 0;'>";
            $htmlBody .= "<p style='color: #666; font-size: 12px;'>This is an automated message from the Events and Shows platform.</p>";
            $htmlBody .= "</div>";
            $htmlBody .= "</body></html>";

            // Try immediate email delivery with fallback to queue
            $this->sendEmailWithFallback($message->to_user_id, $recipient->email, $emailSubject, $htmlBody, $plainTextBody, $messageId);

        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'email', 'failed', $e->getMessage());
            error_log("Email delivery failed: " . $e->getMessage());
        }
    }

    /**
     * Send email with immediate delivery and fallback to queue
     *
     * @param int $userId User ID
     * @param string $email User email address
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param string $plainText Plain text version
     * @param int $messageId Message ID for tracking
     */
    private function sendEmailWithFallback($userId, $email, $subject, $body, $plainText, $messageId) {
        try {
            // Load EmailService for immediate sending attempt
            require_once APPROOT . '/models/EmailService.php';
            $emailService = new EmailService();

            if (DEBUG_MODE) {
                error_log("UnifiedMessageModel::sendEmailWithFallback - Attempting immediate send to: {$email}");
            }

            // Attempt immediate sending with fallback
            $result = $emailService->sendWithFallback($email, $subject, $body, $plainText, $userId);

            if ($result['success']) {
                // Email sent immediately
                $this->trackDelivery($messageId, 'email', 'sent', $result['details']);

                if (DEBUG_MODE) {
                    error_log("UnifiedMessageModel::sendEmailWithFallback - Immediate send successful: {$result['details']}");
                }

            } elseif ($result['queued']) {
                // Email queued for later processing
                $this->trackDelivery($messageId, 'email', 'queued', $result['details']);

                if (DEBUG_MODE) {
                    error_log("UnifiedMessageModel::sendEmailWithFallback - Email queued: {$result['details']}");
                }

            } else {
                // Both immediate and queue failed
                $this->trackDelivery($messageId, 'email', 'failed', $result['details']);

                if (DEBUG_MODE) {
                    error_log("UnifiedMessageModel::sendEmailWithFallback - Both immediate and queue failed: {$result['details']}");
                }
            }

        } catch (Exception $e) {
            // Fallback to old queue method if EmailService fails completely
            $errorMessage = "EmailService failed, using fallback queue: " . $e->getMessage();

            if (DEBUG_MODE) {
                error_log("UnifiedMessageModel::sendEmailWithFallback - EmailService error: " . $e->getMessage());
            }

            try {
                $this->addToNotificationQueue($userId, 'email', $subject, $body);
                $this->trackDelivery($messageId, 'email', 'queued', $errorMessage);

                if (DEBUG_MODE) {
                    error_log("UnifiedMessageModel::sendEmailWithFallback - Fallback queue successful");
                }

            } catch (Exception $queueError) {
                $this->trackDelivery($messageId, 'email', 'failed', $errorMessage . ". Queue error: " . $queueError->getMessage());

                if (DEBUG_MODE) {
                    error_log("UnifiedMessageModel::sendEmailWithFallback - Fallback queue also failed: " . $queueError->getMessage());
                }
            }
        }
    }
    
    /**
     * Deliver message via push notification (FCM)
     */
    private function deliverViaPush($messageId, $message, $senderName) {
        try {
            // Clean content for push notifications
            $cleanSubject = ContentCleaner::cleanForPush($message->subject, 100);
            $cleanMessage = ContentCleaner::cleanForPush($message->message, 400);

            // Create push notification with cleaned content
            $pushTitle = !empty($cleanSubject) ? $cleanSubject : "Message from {$senderName}";
            $pushBody = !empty($cleanMessage) ? $cleanMessage : "You have a new message";

            // ONLY use FCM system for push notifications (no duplicate systems)
            require_once APPROOT . '/models/NotificationModel.php';
            $notificationModel = new NotificationModel();

            // Send via FCM with proper subject and message
            $fcmResult = $notificationModel->sendPushNotification(
                $pushTitle,                    // Title (subject)
                $pushBody,                     // Body (message content)
                $message->to_user_id,          // Target user
                '/public/images/icons/icon-192x192.png',  // Icon
                '/public/images/icons/badge-72x72.png',   // Badge
                '/notification_center',        // Click URL
                [
                    'message_id' => $messageId,
                    'type' => 'message',
                    'from_user' => $senderName
                ]
            );

            // Track final result (only one tracking call)
            if ($fcmResult) {
                $this->trackDelivery($messageId, 'push', 'sent');
            } else {
                $this->trackDelivery($messageId, 'push', 'failed', 'FCM delivery failed');
            }

        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'push', 'failed', $e->getMessage());
            error_log("Push delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via toast notification
     */
    private function deliverViaToast($messageId, $message, $senderName) {
        try {
            // Create toast notification
            $toastTitle = "New Message";
            $toastBody = "From {$senderName}: {$message->subject}";

            // Insert into toast notifications table
            $sql = "INSERT INTO user_toast_notifications
                    (user_id, title, message, event_id, event_type, created_at)
                    VALUES (:user_id, :title, :message, :event_id, 'message', NOW())";

            $this->db->query($sql);
            $this->db->bind(':user_id', $message->to_user_id);
            $this->db->bind(':title', $toastTitle);
            $this->db->bind(':message', $toastBody);
            $this->db->bind(':event_id', $messageId);

            // Track final result (only one tracking call)
            if ($this->db->execute()) {
                $this->trackDelivery($messageId, 'toast', 'sent');
            } else {
                $this->trackDelivery($messageId, 'toast', 'failed', 'Database insert failed');
            }

        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'toast', 'failed', $e->getMessage());
            error_log("Toast delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via SMS
     */
    private function deliverViaSMS($messageId, $message, $senderName) {
        try {
            // Get recipient phone
            $recipient = $this->getUserById($message->to_user_id);
            if (!$recipient || !$recipient->phone) {
                $this->trackDelivery($messageId, 'sms', 'failed', 'No phone number');
                return;
            }

            // Clean content for SMS (removes MIME garbage, limits length)
            $cleanSubject = ContentCleaner::cleanForSms($message->subject, 50);

            // Prepare SMS content
            $smsMessage = "Message from {$senderName}: {$cleanSubject}. View full message in your notification center.";

            // Add to notification queue for SMS processing
            $this->addToNotificationQueue($message->to_user_id, 'sms', 'New Message', $smsMessage);

            // Track successful queuing (SMS will be sent by cron job)
            $this->trackDelivery($messageId, 'sms', 'queued');

        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'sms', 'failed', $e->getMessage());
            error_log("SMS delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Track delivery attempt
     */
    private function trackDelivery($messageId, $method, $status, $errorMessage = null) {
        $sql = "INSERT INTO message_deliveries 
                (message_id, delivery_method, status, sent_at, error_message, attempts, last_attempt, created_at)
                VALUES (:message_id, :method, :status, :sent_at, :error_message, 1, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                sent_at = VALUES(sent_at),
                error_message = VALUES(error_message),
                attempts = attempts + 1,
                last_attempt = NOW()";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':method', $method);
        $this->db->bind(':status', $status);
        $this->db->bind(':sent_at', $status === 'sent' ? gmdate('Y-m-d H:i:s') : null);
        $this->db->bind(':error_message', $errorMessage);
        
        $this->db->execute();
    }

    /**
     * Get delivery status for a message
     */
    public function getMessageDeliveries($messageId) {
        $this->db->query("SELECT * FROM message_deliveries WHERE message_id = :message_id ORDER BY created_at DESC");
        $this->db->bind(':message_id', $messageId);
        return $this->db->resultSet();
    }

    /**
     * Add to notification queue for email/SMS processing
     */
    private function addToNotificationQueue($userId, $type, $subject, $message, $isCritical = false) {
        // Use PHP server time to avoid timezone/server time differences
        $currentTime = date('Y-m-d H:i:s');

        // Mark contact forms as critical to bypass user preferences
        $notificationCategory = $isCritical ? 'critical' : 'standard';

        $sql = "INSERT INTO notification_queue
                (user_id, notification_type, notification_category, subject, message, status, scheduled_for, created_at, updated_at)
                VALUES (:user_id, :type, :category, :subject, :message, 'pending', :scheduled_for, :created_at, :updated_at)";

        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':type', $type);
        $this->db->bind(':category', $notificationCategory);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':message', $message);
        $this->db->bind(':scheduled_for', $currentTime);
        $this->db->bind(':created_at', $currentTime);
        $this->db->bind(':updated_at', $currentTime);

        $this->db->execute();
    }
    
    /**
     * Get all messages for a user (unified view)
     */
    public function getUserMessages($userId, $status = 'all', $limit = 20, $offset = 0) {
        $sql = "SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE m.to_user_id = ?";
        
        // Add status filter
        if ($status === 'unread') {
            $sql .= " AND m.is_read = 0 AND m.is_archived = 0";
        } elseif ($status === 'read') {
            $sql .= " AND m.is_read = 1 AND m.is_archived = 0";
        } elseif ($status === 'archived') {
            $sql .= " AND m.is_archived = 1";
        } else {
            $sql .= " AND m.is_archived = 0"; // Default: exclude archived
        }
        
        $sql .= " ORDER BY m.created_at DESC LIMIT ? OFFSET ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        $this->db->bind(2, $limit);
        $this->db->bind(3, $offset);
        
        $results = $this->db->resultSet();
        
        return $results;
    }
    
    /**
     * Get message counts
     */
    public function getMessageCounts($userId) {
        // Let's debug each query step by step
        
        // Count total active messages (non-archived)
        $sql1 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_archived = 0";
        $this->db->query($sql1);
        $this->db->bind(1, $userId);
        $result1 = $this->db->single();
        $totalCount = (int)$result1->count;
        
        // Count unread messages
        $sql2 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_read = 0 AND is_archived = 0";
        $this->db->query($sql2);
        $this->db->bind(1, $userId);
        $result2 = $this->db->single();
        $unreadCount = (int)$result2->count;
        
        // Count archived messages
        $sql3 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_archived = 1";
        $this->db->query($sql3);
        $this->db->bind(1, $userId);
        $result3 = $this->db->single();
        $archivedCount = (int)$result3->count;
        
        $counts = [
            'total_unread' => $unreadCount,
            'total_count' => $totalCount,
            'archived_count' => $archivedCount
        ];
        
        return $counts;
    }
    
    /**
     * Get message by ID
     */
    public function getMessageById($messageId, $userId = null) {
        if ($userId) {
            // Get message with user permission check and sender info
            $this->db->query("SELECT m.*, 
                                     u.name as from_user_name,
                                     u.email as from_user_email,
                                     s.name as show_title,
                                     s.location as show_location
                              FROM messages m
                              LEFT JOIN users u ON m.from_user_id = u.id
                              LEFT JOIN shows s ON m.show_id = s.id
                              WHERE m.id = :message_id AND (m.to_user_id = :user_id OR m.from_user_id = :user_id2)");
            $this->db->bind(':message_id', $messageId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':user_id2', $userId);
        } else {
            // Get message without permission check (for admin use)
            $this->db->query("SELECT m.*, 
                                     u.name as from_user_name,
                                     u.email as from_user_email,
                                     s.name as show_title,
                                     s.location as show_location
                              FROM messages m
                              LEFT JOIN users u ON m.from_user_id = u.id
                              LEFT JOIN shows s ON m.show_id = s.id
                              WHERE m.id = :message_id");
            $this->db->bind(':message_id', $messageId);
        }
        return $this->db->single();
    }
    

    
    /**
     * Get user by ID
     */
    private function getUserById($userId) {
        $this->db->query("SELECT id, name, email, phone FROM users WHERE id = :user_id");
        $this->db->bind(':user_id', $userId);
        return $this->db->single();
    }
    
    /**
     * Mark message as read
     */
    public function markAsRead($messageId, $userId) {
        $this->db->query("UPDATE messages 
                         SET is_read = 1, read_at = NOW() 
                         WHERE id = :message_id AND to_user_id = :user_id");
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':user_id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Mark all messages as read for a user
     */
    public function markAllAsRead($userId, $type = 'all') {
        $sql = "UPDATE messages 
                SET is_read = 1, read_at = NOW() 
                WHERE to_user_id = :user_id AND is_read = 0";
        
        $params = [':user_id' => $userId];
        
        // Add type filter if specified
        if ($type !== 'all') {
            $sql .= " AND message_type = :type";
            $params[':type'] = $type;
        }
        
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind($key, $value);
        }
        
        return $this->db->execute();
    }
    
    /**
     * Send reply to a message
     */
    public function sendReply($fromUserId, $toUserId, $originalSubject, $message, $parentMessageId, $showId = null) {
        $subject = strpos($originalSubject, 'Re: ') === 0 ? $originalSubject : 'Re: ' . $originalSubject;
        
        // Get the original message to inherit its message_type
        $messageType = 'direct'; // Default fallback
        if ($parentMessageId) {
            $this->db->query("SELECT message_type FROM messages WHERE id = :parent_id");
            $this->db->bind(':parent_id', $parentMessageId);
            $parentMessage = $this->db->single();
            
            if ($parentMessage && $parentMessage->message_type) {
                $messageType = $parentMessage->message_type;
                error_log("UnifiedMessageModel::sendReply - Inherited message_type '$messageType' from parent message ID: $parentMessageId");
            }
        }
        
        return $this->sendMessage($fromUserId, $toUserId, $subject, $message, $showId, $messageType, false, $parentMessageId);
    }
    
    /**
     * Get message thread (original message + all replies)
     * Returns messages in chronological order for threaded display
     */
    public function getMessageThread($messageId, $userId) {
        // First, get the original message to find the root of the thread
        $originalMessage = $this->getMessageById($messageId, $userId);
        if (!$originalMessage) {
            return [];
        }
        
        // Find the root message ID (either this message or its parent)
        $rootMessageId = $originalMessage->parent_message_id ? $originalMessage->parent_message_id : $messageId;
        
        // Get all messages in this thread (root + all replies)
        $sql = "SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE (m.id = ? OR m.parent_message_id = ?)
                AND (m.to_user_id = ? OR m.from_user_id = ?)
                ORDER BY m.created_at ASC";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootMessageId);
        $this->db->bind(2, $rootMessageId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        $results = $this->db->resultSet();
        
        // Mark all unread messages in this thread as read
        $this->markThreadAsRead($rootMessageId, $userId);
        
        // Organize messages into thread structure
        $thread = [];
        foreach ($results as $msg) {
            $thread[] = $msg;
        }
        
        return $thread;
    }
    
    /**
     * Archive entire thread (parent + all replies)
     */
    public function archiveThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Archive the root message and all its replies
        $sql = "UPDATE messages 
                SET is_archived = 1 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get conversation counts (grouped by thread)
     */
    public function getConversationCounts($userId) {
        // Count all conversations (root messages only)
        $sql1 = "SELECT COUNT(*) as count FROM messages 
                WHERE to_user_id = ? AND parent_message_id IS NULL AND is_archived = 0";
        $this->db->query($sql1);
        $this->db->bind(1, $userId);
        $result1 = $this->db->single();
        $allCount = (int)$result1->count;
        
        // Count unread conversations (root messages with unread messages in thread)
        $sql2 = "SELECT COUNT(DISTINCT COALESCE(m.parent_message_id, m.id)) as count 
                FROM messages m 
                WHERE m.to_user_id = ? AND m.is_read = 0 AND m.is_archived = 0";
        $this->db->query($sql2);
        $this->db->bind(1, $userId);
        $result2 = $this->db->single();
        $unreadCount = (int)$result2->count;
        
        // Count archived conversations (root messages only)
        $sql3 = "SELECT COUNT(*) as count FROM messages 
                WHERE to_user_id = ? AND parent_message_id IS NULL AND is_archived = 1";
        $this->db->query($sql3);
        $this->db->bind(1, $userId);
        $result3 = $this->db->single();
        $archivedCount = (int)$result3->count;
        
        return [
            'all' => $allCount,
            'unread' => $unreadCount,
            'archived' => $archivedCount
        ];
    }
    
    /**
     * Delete entire thread (parent + all replies)
     */
    public function deleteThread($messageId, $userId) {
        try {
            $this->db->beginTransaction();

            // First get the root message ID
            $rootId = $this->getThreadRootId($messageId, $userId);
            if (!$rootId) {
                $this->db->rollback();
                return false;
            }

            // Get all ticket numbers that will be deleted (for cleanup)
            $this->db->query("SELECT DISTINCT ticket_number FROM messages
                             WHERE (id = ? OR parent_message_id = ?)
                             AND (from_user_id = ? OR to_user_id = ?)
                             AND ticket_number IS NOT NULL");
            $this->db->bind(1, $rootId);
            $this->db->bind(2, $rootId);
            $this->db->bind(3, $userId);
            $this->db->bind(4, $userId);
            $ticketsToCleanup = $this->db->resultSet();

            // Delete the root message and all its replies
            $sql = "DELETE FROM messages
                    WHERE (id = ? OR parent_message_id = ?)
                    AND (from_user_id = ? OR to_user_id = ?)";

            $this->db->query($sql);
            $this->db->bind(1, $rootId);
            $this->db->bind(2, $rootId);
            $this->db->bind(3, $userId);
            $this->db->bind(4, $userId);

            $result = $this->db->execute();

            if ($result && !empty($ticketsToCleanup)) {
                // Clean up orphaned ticket numbers
                foreach ($ticketsToCleanup as $ticket) {
                    $this->cleanupOrphanedTicket($ticket->ticket_number);
                }

                error_log("UnifiedMessageModel::deleteThread - Cleaned up " . count($ticketsToCleanup) . " ticket numbers");
            }

            $this->db->commit();
            return $result;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("UnifiedMessageModel::deleteThread - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up orphaned ticket number (ticket exists but no messages reference it)
     */
    private function cleanupOrphanedTicket($ticketNumber) {
        try {
            // Check if any messages still reference this ticket
            $this->db->query("SELECT COUNT(*) as count FROM messages WHERE ticket_number = :ticket_number");
            $this->db->bind(':ticket_number', $ticketNumber);
            $result = $this->db->single();

            if ($result && $result->count == 0) {
                // No messages reference this ticket - it's orphaned, clean it up

                // Clean up from ticket_numbers table if it exists
                $this->db->query("DELETE FROM ticket_numbers WHERE ticket_number = :ticket_number");
                $this->db->bind(':ticket_number', $ticketNumber);
                $this->db->execute();

                error_log("UnifiedMessageModel::cleanupOrphanedTicket - Cleaned up orphaned ticket: " . $ticketNumber);
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("UnifiedMessageModel::cleanupOrphanedTicket - Error cleaning ticket {$ticketNumber}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get the root message ID for a thread
     */
    private function getThreadRootId($messageId, $userId) {
        $sql = "SELECT id, parent_message_id 
                FROM messages 
                WHERE id = ? 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        $message = $this->db->single();
        if (!$message) {
            return false;
        }
        
        $rootId = $message->parent_message_id ?: $message->id;
        
        // If this message has a parent, return the parent ID, otherwise return this message's ID
        return $rootId;
    }
    
    /**
     * Get thread info for display warnings
     */
    public function getThreadInfo($messageId, $userId) {
        // First, verify the message exists and user has access
        $sql = "SELECT id, parent_message_id FROM messages 
                WHERE id = ? 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        $message = $this->db->single();
        if (!$message) {
            return null; // Message not found or no access
        }
        
        // Get the root message ID
        $rootId = $message->parent_message_id ?: $message->id;
        
        // Count all messages in the thread
        $sql = "SELECT 
                    COUNT(*) as total_messages,
                    SUM(CASE WHEN from_user_id = ? AND to_user_id = ? THEN 1 ELSE 0 END) as self_replies
                FROM messages 
                WHERE (id = ? OR parent_message_id = ?)
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $rootId);
        $this->db->bind(4, $rootId);
        $this->db->bind(5, $userId);
        $this->db->bind(6, $userId);
        
        $result = $this->db->single();
        
        return $result ?: (object)['total_messages' => 1, 'self_replies' => 0];
    }
    
    /**
     * Mark all messages in a thread as read
     */
    private function markThreadAsRead($rootMessageId, $userId) {
        $sql = "UPDATE messages 
                SET is_read = 1, read_at = NOW() 
                WHERE (id = ? OR parent_message_id = ?) 
                AND to_user_id = ? 
                AND is_read = 0";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootMessageId);
        $this->db->bind(2, $rootMessageId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }

    /**
     * Get global notification settings from notification_settings table
     */
    private function getGlobalNotificationSettings() {
        try {
            $globalSettings = $this->notificationModel->getNotificationSettings();

            // Return with defaults if settings don't exist
            return [
                'email_enabled' => $globalSettings['email_enabled'] ?? true,
                'sms_enabled' => $globalSettings['sms_enabled'] ?? true,
                'push_enabled' => $globalSettings['push_enabled'] ?? true,
                'toast_enabled' => $globalSettings['toast_enabled'] ?? true
            ];
        } catch (Exception $e) {
            // If there's an error accessing global settings, default to enabled
            // This ensures messages still work if there's a configuration issue
            error_log("UnifiedMessageModel: Error getting global settings - " . $e->getMessage());
            return [
                'email_enabled' => true,
                'sms_enabled' => true,
                'push_enabled' => true,
                'toast_enabled' => true
            ];
        }
    }

    /**
     * Get user notification preferences
     */
    private function getUserNotificationPreferences($userId) {
        $this->db->query("SELECT email_notifications as email_enabled,
                                sms_notifications as sms_enabled,
                                push_notifications as push_enabled,
                                toast_notifications as toast_enabled
                         FROM user_notification_preferences WHERE user_id = :user_id");
        $this->db->bind(':user_id', $userId);
        return $this->db->single();
    }
    
    /**
     * Create default notification preferences
     */
    private function createDefaultNotificationPreferences($userId) {
        $sql = "INSERT INTO user_notification_preferences 
                (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications, 
                 event_reminders, registration_updates, judging_updates, award_notifications, system_announcements)
                VALUES (:user_id, 1, 0, 1, 1, 1, 1, 1, 1, 1)
                ON DUPLICATE KEY UPDATE
                email_notifications = COALESCE(email_notifications, 1),
                push_notifications = COALESCE(push_notifications, 1),
                toast_notifications = COALESCE(toast_notifications, 1)";
        
        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        return $this->db->execute();
    }
    
    /**
     * Check if user can send notifications
     * Now checks both global settings and user preferences
     */
    public function canUserSendNotifications($userId) {
        // Get global settings first
        $globalSettings = $this->getGlobalNotificationSettings();

        // Get user preferences
        $preferences = $this->getUserNotificationPreferences($userId);

        if (!$preferences) {
            $this->createDefaultNotificationPreferences($userId);
            $preferences = $this->getUserNotificationPreferences($userId);
        }

        // Check if any notification type is enabled both globally AND for the user
        return (($globalSettings['email_enabled'] && $preferences->email_enabled) ||
                ($globalSettings['push_enabled'] && $preferences->push_enabled) ||
                ($globalSettings['toast_enabled'] && $preferences->toast_enabled) ||
                ($globalSettings['sms_enabled'] && $preferences->sms_enabled));
    }
    
    /**
     * Check if a user can reply to a specific message
     */
    public function canUserReplyToMessage($userId, $messageId) {
        error_log("UNIFIED-REPLY-DEBUG: Checking reply permission for user $userId, message $messageId");

        // Get the message
        $message = $this->getMessageById($messageId, $userId);

        error_log("UNIFIED-REPLY-DEBUG: Message found: " . ($message ? 'YES' : 'NO'));

        if (!$message) {
            error_log("UNIFIED-REPLY-DEBUG: Message not found or no access");
            return false; // Message doesn't exist or user doesn't have access
        }

        error_log("UNIFIED-REPLY-DEBUG: Message details - message_type: {$message->message_type}, requires_reply: {$message->requires_reply}, allows_reply: " . ($message->allows_reply ?? 'NULL'));

        // Check if user has privileged role (admin/coordinator/judge/staff can reply to anything)
        require_once APPROOT . '/core/Auth.php';
        $auth = new Auth();
        $hasPrivilegedRole = $auth->hasRole(['admin', 'coordinator', 'judge', 'staff']);
        error_log("UNIFIED-REPLY-DEBUG: User has privileged role: " . ($hasPrivilegedRole ? 'YES' : 'NO'));

        if ($hasPrivilegedRole) {
            error_log("UNIFIED-REPLY-DEBUG: Allowing reply due to privileged role");
            return true;
        }

        // Email messages can always be replied to (regardless of [reply] token)
        if ($message->message_type === 'email') {
            error_log("UNIFIED-REPLY-DEBUG: Allowing reply because message_type is email");
            return true;
        }

        // Direct messages can always be replied to
        if ($message->message_type === 'direct') {
            error_log("UNIFIED-REPLY-DEBUG: Allowing reply because message_type is direct");
            return true;
        }

        // For other message types: check if message allows reply (has [reply] token)
        if (!$message->requires_reply) {
            error_log("UNIFIED-REPLY-DEBUG: Message doesn't allow replies - no [reply] token (requires_reply: {$message->requires_reply})");
            return false;
        }
        
        // Check if user has already replied to this message
        $sql = "SELECT COUNT(*) as reply_count 
                FROM messages 
                WHERE parent_message_id = :message_id 
                AND from_user_id = :user_id";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':user_id', $userId);
        
        $result = $this->db->single();
        
        // For now, allow multiple replies (like a conversation)
        // If you want to limit to one reply, uncomment the next line:
        // return $result->reply_count == 0;

        error_log("UNIFIED-REPLY-DEBUG: Allowing multiple replies - returning TRUE");
        return true; // Allow multiple replies for conversation-style messaging
    }
    
    /**
     * Archive a message
     */
    public function archiveMessage($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_archived = 1 
                WHERE id = :message_id AND to_user_id = :user_id";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':user_id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Unarchive a message
     */
    public function unarchiveMessage($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_archived = 0 
                WHERE id = ? AND to_user_id = ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Unarchive entire thread (parent + all replies)
     */
    public function unarchiveThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Unarchive the root message and all its replies
        $sql = "UPDATE messages 
                SET is_archived = 0 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Delete a message permanently
     */
    public function deleteMessage($messageId, $userId) {
        $sql = "DELETE FROM messages 
                WHERE id = ? AND to_user_id = ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Create a notification item (equivalent to old NotificationCenterModel::createNotificationItem)
     */
    public function createNotificationItem($userId, $type, $title, $message, $sourceTable = null, $sourceId = null, $actionUrl = null, $actionText = null, $metadata = null) {
        try {
            $sql = "INSERT INTO messages (
                        from_user_id, to_user_id, subject, message, 
                        show_id, requires_reply, is_read, is_archived,
                        source_table, source_id, action_url, action_text,
                        metadata, created_at, updated_at
                    ) VALUES (
                        1, :to_user_id, :subject, :message,
                        NULL, 0, 0, 0,
                        :source_table, :source_id, :action_url, :action_text,
                        :metadata, NOW(), NOW()
                    )";
            
            $this->db->query($sql);
            $this->db->bind(':to_user_id', $userId);
            $this->db->bind(':subject', $title);
            $this->db->bind(':message', $message);
            $this->db->bind(':source_table', $sourceTable);
            $this->db->bind(':source_id', $sourceId);
            $this->db->bind(':action_url', $actionUrl);
            $this->db->bind(':action_text', $actionText);
            $this->db->bind(':metadata', $metadata ? json_encode($metadata) : null);
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }
            return false;
            
        } catch (Exception $e) {
            error_log("UnifiedMessageModel::createNotificationItem - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Cleanup old archived notifications
     */
    public function cleanupOldArchivedNotifications($daysAfterShowEnd = 14) {
        try {
            $sql = "DELETE FROM messages 
                    WHERE is_archived = 1 
                    AND created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
            
            $this->db->query($sql);
            $this->db->bind(':days', $daysAfterShowEnd);
            
            return $this->db->execute();
            
        } catch (Exception $e) {
            error_log("UnifiedMessageModel::cleanupOldArchivedNotifications - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Convert URLs in text to clickable HTML links
     *
     * @param string $text Text to process
     * @return string Text with URLs converted to links
     */
    private function convertUrlsToLinks($text) {
        // Escape HTML first
        $text = htmlspecialchars($text);

        // Pattern to match URLs
        $pattern = '/\b(?:https?:\/\/|www\.)[^\s<>"\']+/i';

        return preg_replace_callback($pattern, function($matches) {
            $url = $matches[0];

            // Add http:// if the URL starts with www.
            $href = (strpos($url, 'http') === 0) ? $url : 'http://' . $url;

            // Create a clickable link with styling
            return '<a href="' . htmlspecialchars($href) . '" style="color: #007bff; text-decoration: none;" target="_blank">' . htmlspecialchars($url) . '</a>';
        }, $text);
    }

    /**
     * Generate security token for ticket
     */
    private function generateSecurityToken($length = 6) {
        return substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, $length);
    }

    /**
     * Create email message with role-based ticket generation
     */
    public function createEmailMessage($fromUserId, $toUserId, $subject, $message, $originalSenderEmail = null, $showId = null, $priority = 'normal', $existingTicketNumber = null, $existingSecurityToken = null) {
        try {
            // Check if we already have a ticket number and security token (from email processing)
            error_log("UnifiedMessageModel::createEmailMessage - Checking existing ticket params: ticketNumber='" . ($existingTicketNumber ?? 'NULL') . "', securityToken='" . ($existingSecurityToken ?? 'NULL') . "'");
            
            if ($existingTicketNumber && $existingSecurityToken) {
                // Use existing ticket number and security token (this is a reply to an existing conversation)
                $ticketNumber = $existingTicketNumber;
                $securityToken = $existingSecurityToken;
                error_log("UnifiedMessageModel::createEmailMessage - Using existing ticket: " . $ticketNumber . " with token: " . $securityToken);
            } else {
                // Generate new ticket number and security token (this is a new conversation)
                error_log("UnifiedMessageModel::createEmailMessage - No existing ticket/token provided, generating new");
                $senderRole = $this->getUserRole($fromUserId);
                $ticketNumber = $this->ticketService->generateTicketNumber($senderRole, $showId, $fromUserId);
                $securityToken = $this->generateSecurityToken();
                error_log("UnifiedMessageModel::createEmailMessage - Generated new ticket: " . $ticketNumber . " with token: " . $securityToken);
            }

            // Create the email message
            $messageId = $this->sendMessage(
                $fromUserId,
                $toUserId,
                $subject,
                $message,
                $showId,
                'email',
                false, // requires_reply
                null // parent_message_id - will be set if this is a reply
            );

            if ($messageId) {
                // Update with email-specific fields including security token
                $this->db->query("UPDATE messages SET
                                 ticket_number = :ticket_number,
                                 security_token = :security_token,
                                 original_sender_email = :original_sender_email
                                 WHERE id = :message_id");
                $this->db->bind(':ticket_number', $ticketNumber);
                $this->db->bind(':security_token', $securityToken);
                $this->db->bind(':original_sender_email', $originalSenderEmail);
                $this->db->bind(':message_id', $messageId);
                $this->db->execute();

                return $messageId;
            }

            return false;
        } catch (Exception $e) {
            error_log("UnifiedMessageModel::createEmailMessage - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create email reply with proper threading
     */
    public function createEmailReply($fromUserId, $originalMessageId, $subject, $message, $recipientEmail = null) {
        try {
            // Get original message details
            $this->db->query("SELECT * FROM messages WHERE id = :message_id");
            $this->db->bind(':message_id', $originalMessageId);
            $originalMessage = $this->db->single();

            if (!$originalMessage) {
                throw new Exception("Original message not found");
            }

            // Find the root message of the conversation
            $parentMessageId = $originalMessage->parent_message_id ?: $originalMessageId;

            // Determine sender role
            $senderRole = $this->getUserRole($fromUserId);

            // Use the same ticket number and security token as the original message
            $ticketNumber = $originalMessage->ticket_number;
            $securityToken = $originalMessage->security_token;

            // Create the reply
            $replyId = $this->sendMessage(
                $fromUserId,
                $originalMessage->from_user_id, // Reply to original sender
                $subject,
                $message,
                $originalMessage->show_id,
                'email',
                false, // requires_reply
                $parentMessageId
            );

            if ($replyId) {
                // Update with email-specific fields including security token
                $this->db->query("UPDATE messages SET
                                 ticket_number = :ticket_number,
                                 security_token = :security_token,
                                 original_sender_email = :recipient_email
                                 WHERE id = :message_id");
                $this->db->bind(':ticket_number', $ticketNumber);
                $this->db->bind(':security_token', $securityToken);
                $this->db->bind(':recipient_email', $recipientEmail);
                $this->db->bind(':message_id', $replyId);
                $this->db->execute();

                return $replyId;
            }

            return false;
        } catch (Exception $e) {
            error_log("UnifiedMessageModel::createEmailReply - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get email messages for role-based access
     */
    public function getEmailMessages($userId, $userRole, $status = 'all', $limit = 20, $offset = 0) {
        $sql = "SELECT m.*, u.name as from_user_name, s.name as show_title, s.location as show_location,
                       owner.name as owner_name
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                LEFT JOIN users owner ON m.owned_by_admin_id = owner.id
                WHERE m.message_type = 'email'";

        // Status filtering
        if ($status === 'unread') {
            $sql .= " AND m.is_read = 0";
        } elseif ($status === 'archived') {
            $sql .= " AND m.is_archived = 1";
        } else {
            $sql .= " AND m.is_archived = 0";
        }

        // Role-based filtering
        if ($userRole === 'coordinator') {
            // Coordinators only see emails for their shows
            $sql .= " AND (m.ticket_number LIKE 'RER-C%'
                     AND m.show_id IN (
                         SELECT show_id FROM show_role_assignments
                         WHERE user_id = :user_id AND assigned_role = 'coordinator' AND is_active = 1
                     ))";

            $sql .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        } elseif ($userRole === 'admin') {
            // Admins see all email messages - use separate parameters for each occurrence
            $sql .= " AND (m.to_user_id = :user_id1 OR m.from_user_id = :user_id2 OR m.owned_by_admin_id = :user_id3 OR m.owned_by_admin_id IS NULL)";
            $sql .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";

            $this->db->query($sql);
            $this->db->bind(':user_id1', $userId);
            $this->db->bind(':user_id2', $userId);
            $this->db->bind(':user_id3', $userId);
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        } else {
            // Other roles don't have email access
            return [];
        }

        return $this->db->resultSet();
    }

    /**
     * Get email message count for role-based access
     */
    public function getEmailMessageCount($userId, $userRole, $status = 'all') {
        $sql = "SELECT COUNT(*) as count FROM messages m
                WHERE m.message_type = 'email'";

        // Status filtering
        if ($status === 'unread') {
            $sql .= " AND m.is_read = 0";
        } elseif ($status === 'archived') {
            $sql .= " AND m.is_archived = 1";
        } else {
            $sql .= " AND m.is_archived = 0";
        }

        // Role-based filtering
        if ($userRole === 'coordinator') {
            // Coordinators only see emails for their shows
            $sql .= " AND (m.ticket_number LIKE 'RER-C%'
                     AND m.show_id IN (
                         SELECT show_id FROM show_role_assignments
                         WHERE user_id = :user_id AND assigned_role = 'coordinator' AND is_active = 1
                     ))";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
        } elseif ($userRole === 'admin') {
            // Admins see all email messages - use separate parameters for each occurrence
            $sql .= " AND (m.to_user_id = :user_id1 OR m.from_user_id = :user_id2 OR m.owned_by_admin_id = :user_id3 OR m.owned_by_admin_id IS NULL)";

            $this->db->query($sql);
            $this->db->bind(':user_id1', $userId);
            $this->db->bind(':user_id2', $userId);
            $this->db->bind(':user_id3', $userId);
        } else {
            // Other roles don't have email access
            return 0;
        }

        $result = $this->db->single();

        return $result ? (int)$result->count : 0;
    }

    /**
     * Get user role
     */
    private function getUserRole($userId) {
        $this->db->query("SELECT role FROM users WHERE id = :user_id");
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();

        return $result ? $result->role : 'user';
    }

    /**
     * Transfer email ownership (admin only)
     */
    public function transferEmailOwnership($messageId, $newOwnerId, $transferredBy) {
        try {
            $this->db->query("UPDATE messages SET owned_by_admin_id = :new_owner_id WHERE id = :message_id AND message_type = 'email'");
            $this->db->bind(':new_owner_id', $newOwnerId);
            $this->db->bind(':message_id', $messageId);

            if ($this->db->execute()) {
                // Create system message about transfer
                $this->sendMessage(
                    $transferredBy,
                    $newOwnerId,
                    'Email Ownership Transfer',
                    "Email message ownership has been transferred to you. Message ID: {$messageId}",
                    null,
                    'system',
                    false,
                    null
                );

                return true;
            }

            return false;
        } catch (Exception $e) {
            error_log("UnifiedMessageModel::transferEmailOwnership - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Move email to folder (admin only)
     */
    public function moveEmailToFolder($messageId, $folderId) {
        try {
            $this->db->query("UPDATE messages SET folder_id = :folder_id WHERE id = :message_id AND message_type = 'email'");
            $this->db->bind(':folder_id', $folderId);
            $this->db->bind(':message_id', $messageId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log("UnifiedMessageModel::moveEmailToFolder - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get role-based from address for emails
     */
    public function getRoleBasedFromAddress($senderRole, $message = null) {
        // Get admin email settings
        require_once APPROOT . '/models/SettingsModel.php';
        $settingsModel = new SettingsModel();

        $adminFromEmail = $settingsModel->getSetting('email_from_address', '<EMAIL>');
        $adminFromName = $settingsModel->getSetting('email_from_name', 'Events and Shows');

        switch ($senderRole) {
            case 'admin':
            case 'coordinator':
                // Admin and coordinators use admin email settings (no personal info exposed)
                return [
                    'email' => $adminFromEmail,
                    'name' => $adminFromName
                ];

            case 'judge':
            case 'staff':
                // Judges and staff use no-reply address
                return [
                    'email' => '<EMAIL>',
                    'name' => 'Events and Shows - No Reply'
                ];

            default:
                // System and other roles use admin settings
                return [
                    'email' => $adminFromEmail,
                    'name' => $adminFromName
                ];
        }
    }

    /**
     * Check if role should use no-reply email
     */
    public function isNoReplyRole($senderRole) {
        return in_array($senderRole, ['judge', 'staff']);
    }
}